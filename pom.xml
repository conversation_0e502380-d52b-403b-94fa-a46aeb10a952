<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>4671abf7-f2d5-45a1-9ee4-7b645bf0fa49</groupId>
	<artifactId>3degrees-netsuite-sys-api</artifactId>
	<version>1.3.39-SNAPSHOT</version>
	<packaging>mule-application</packaging>
  
	<name>3degrees-netsuite-sys-api</name>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

		<app.runtime>4.5.1</app.runtime>
		<mule.maven.plugin.version>4.2.0</mule.maven.plugin.version>
	</properties>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-clean-plugin</artifactId>
				<version>3.2.0</version>
			</plugin>
			<plugin>
				<groupId>org.mule.tools.maven</groupId>
				<artifactId>mule-maven-plugin</artifactId>
				<version>${mule.maven.plugin.version}</version>
				<extensions>true</extensions>
				<configuration>
					<sharedLibraries>
						<sharedLibrary>
							<groupId>mysql</groupId>
							<artifactId>mysql-connector-java</artifactId>
						</sharedLibrary>
					</sharedLibraries>
					<cloudhub2Deployment>
						<javaVersion>17</javaVersion>
						<releaseChannel>LTS</releaseChannel>
						<uri>https://anypoint.mulesoft.com</uri>
						<provider>MC</provider>
						<environment>${environment}</environment>
						<target>${target}</target>
						<muleVersion>${app.runtime}</muleVersion>
						<server>${server}</server>
						<businessGroupId>${businessGroupId}</businessGroupId>
						<applicationName>${app.name}</applicationName>
						<replicas>${replicas}</replicas>
						<vCores>${vCores}</vCores>

						<deploymentSettings>
							<generateDefaultPublicUrl>${generateDefaultPublicUrl}</generateDefaultPublicUrl>
							<http>
								<inbound>
									<lastMileSecurity>true</lastMileSecurity>
									<forwardSslSession>false</forwardSslSession>
								</inbound>
							</http>
						</deploymentSettings>
						<properties>
							<http.port>8081</http.port>
							<https.port>8082</https.port>
							<mule.env>${mule.env}</mule.env>
						</properties>
						<secureProperties>
							<mule.key>${mule.key}</mule.key>
							<anypoint.platform.client_id>${anypoint.platform.client_id}</anypoint.platform.client_id>
							<anypoint.platform.client_secret>${anypoint.platform.client_secret}</anypoint.platform.client_secret>
						</secureProperties>
						<skipDeploymentVerification>${skipDeploymentVerification}</skipDeploymentVerification>
					</cloudhub2Deployment>

					<classifier>mule-application</classifier>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<distributionManagement>
		<repository>
			<id>anypoint-connected-app</id>
			<name>Corporate Repository</name>
			<url>https://maven.anypoint.mulesoft.com/api/v3/organizations/${project.groupId}/maven</url>
			<layout>default</layout>
		</repository>
	</distributionManagement>

	<dependencies>
		<dependency>
			<groupId>org.mule.connectors</groupId>
			<artifactId>mule-http-connector</artifactId>
			<version>1.9.3</version>
			<classifier>mule-plugin</classifier>
		</dependency>
		<dependency>
			<groupId>org.mule.connectors</groupId>
			<artifactId>mule-sockets-connector</artifactId>
			<version>1.2.4</version>
			<classifier>mule-plugin</classifier>
		</dependency>
		<dependency>
			<groupId>org.mule.modules</groupId>
			<artifactId>mule-apikit-module</artifactId>
			<version>1.10.4</version>
			<classifier>mule-plugin</classifier>
		</dependency>
		<dependency>
			<groupId>com.mulesoft.modules</groupId>
			<artifactId>mule-secure-configuration-property-module</artifactId>
			<version>1.2.7</version>
			<classifier>mule-plugin</classifier>
		</dependency>
		<dependency>
			<groupId>com.mulesoft.connectors</groupId>
			<artifactId>mule-netsuite-connector</artifactId>
			<version>11.9.2</version>
			<classifier>mule-plugin</classifier>
		</dependency>
		<dependency>
			<groupId>org.mule.connectors</groupId>
			<artifactId>mule-db-connector</artifactId>
			<version>1.14.11</version>
			<classifier>mule-plugin</classifier>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>8.0.30</version>
		</dependency>
		<dependency>
			<groupId>org.mule.modules</groupId>
			<artifactId>mule-validation-module</artifactId>
			<version>2.0.6</version>
			<classifier>mule-plugin</classifier>
		</dependency>
		<dependency>
			<groupId>a970b687-ceb1-48a0-9bc7-6fed0e331363</groupId>
			<artifactId>3degrees-netsuite-sys-api</artifactId>
			<version>1.0.58</version>
			<classifier>raml</classifier>
			<type>zip</type>
		</dependency>
	</dependencies>

	<repositories>
		<repository>
			<id>anypoint-connected-app</id>
			<name>Anypoint Exchange</name>
			<url>https://maven.anypoint.mulesoft.com/api/v3/maven</url>
			<layout>default</layout>
		</repository>
		<repository>
			<id>mulesoft-releases</id>
			<name>MuleSoft Releases Repository</name>
			<url>https://repository.mulesoft.org/releases/</url>
			<layout>default</layout>
		</repository>
		<repository>
			<id>anypoint-exchange-v3</id>
			<name>Anypoint Exchange V3</name>
			<url>https://maven.anypoint.mulesoft.com/api/v3/maven</url>
			<layout>default</layout>
		</repository>
	</repositories>

	<pluginRepositories>
		<pluginRepository>
			<id>mulesoft-releases</id>
			<name>MuleSoft Releases Repository</name>
			<layout>default</layout>
			<url>https://repository.mulesoft.org/releases/</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</pluginRepository>
	</pluginRepositories>

</project>
