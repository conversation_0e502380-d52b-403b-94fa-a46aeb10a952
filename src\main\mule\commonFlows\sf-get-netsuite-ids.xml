<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns:db="http://www.mulesoft.org/schema/mule/db"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/db http://www.mulesoft.org/schema/mule/db/current/mule-db.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd">
	<sub-flow name="sf-get-netsuite-internal-id" doc:id="aa648170-b358-4bfd-aa8e-5e161e8be6c5" >
		<!-- <db:select doc:name="Retrieve from REF_ID table" doc:id="5cf3ee1e-37c5-4a77-aa85-1c6aec099039" config-ref="Database_Config_Transactions" target="vRefIdResponse">
			<db:sql ><![CDATA[SELECT NETSUITE_ID FROM REF_ID WHERE ENTERPRISE_ID = :enterpriseId LIMIT 1]]></db:sql>
			<db:input-parameters ><![CDATA[#[{
	"enterpriseId": vars.vEId
}]]]></db:input-parameters>
		</db:select> -->
		<http:request method="GET" doc:name="Retrieve from REF_ID" doc:id="4643ebfe-1fe5-4fe2-82e7-46c68c7cd060" config-ref="HTTPS_Request_Transaction_DB_SYS_API" path="#[p('https.request.dbSysApi.refId.path')]" target="vRefIdResponse">
			<http:headers ><![CDATA[#[output application/java
---
vars.vRequestAttributes.'headers' default vars.vAttributes.headers]]]></http:headers>
			<http:query-params ><![CDATA[#[output application/java
---
{
	"ENTERPRISE_ID" : vars.vEnterpriseId
}]]]></http:query-params>
		</http:request>
		<set-payload value="#[output application/json&#10;---&#10;vars.vRefIdResponse.response[0].'NETSUITE_ID' default &quot;&quot;]" doc:name="Set Payload" doc:id="352c042a-8298-454e-8212-2dfe33797a28" />
	</sub-flow>
	<sub-flow name="sf-get-netsuite-enterprise-id" doc:id="6f9e51e2-778a-46d1-9d37-f7d3e938dbd4" >
		<!-- <db:select doc:name="Retrieve from REF_ID table" doc:id="d5521093-0abd-4f9b-961d-57607e73ab30" config-ref="Database_Config_Transactions" target="vRefIdResponse" >
			<db:sql ><![CDATA[SELECT ENTERPRISE_ID FROM REF_ID WHERE NETSUITE_ID = :netsuiteId LIMIT 1]]></db:sql>
			<db:input-parameters ><![CDATA[#[{
	"netsuiteId": vars.vInternalId
}]]]></db:input-parameters>
		</db:select> -->
		<http:request method="GET" doc:name="Retrieve from REF_ID" doc:id="0f8391c9-66e5-48c2-854b-d1d08f6a50be" target="vRefIdResponse" config-ref="HTTPS_Request_Transaction_DB_SYS_API" path="#[p('https.request.dbSysApi.refId.path')]">
			<http:headers ><![CDATA[#[output application/java
---
vars.vRequestAttributes.'headers' default vars.vAttributes.headers]]]></http:headers>
			<http:query-params ><![CDATA[#[output application/java
---
{
	"OBJECT_TYPE": vars.vRecordType,
	"NETSUITE_ID" : vars.vInternalId
}]]]></http:query-params>
		</http:request>
		<set-payload value="#[output application/json&#10;---&#10;vars.vRefIdResponse.response[0].'ENTERPRISE_ID' default &quot;&quot;]" doc:name="Set Payload" doc:id="a90fcfb9-3f32-4f27-9f06-5f1f2b3bd67d" />
	</sub-flow>
</mule>
