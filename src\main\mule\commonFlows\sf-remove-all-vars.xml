<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd">
	<sub-flow name="sf-remove-all-vars" doc:id="08c2b01d-abf6-4936-921a-ea91030480de" >
		<async doc:name="Async" doc:id="a21b4586-4877-42d4-b6bd-b816b87a4d9c" >
			<foreach doc:name="For Each variable" doc:id="c060dc31-cf77-4453-8f53-88d395b49020" collection='#[var excludeList = ["httpStatus", "outboundHeaders"]&#10;output application/json&#10;---&#10;(vars pluck($$)) -- excludeList]'>
			<remove-variable doc:name="Remove Variable" doc:id="7f87c15e-c233-4fcc-a0fa-afcacf0d66ef" variableName="#[payload]" />
		</foreach>
		</async>
	</sub-flow>
</mule>
