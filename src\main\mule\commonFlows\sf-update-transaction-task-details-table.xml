<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns:db="http://www.mulesoft.org/schema/mule/db"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/db http://www.mulesoft.org/schema/mule/db/current/mule-db.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd">
	<sub-flow name="sf-update-transaction-task-details-table" doc:id="b6a9a305-30a2-4f27-9fea-606aeb3c877d" >
		<http:request method="PUT" doc:name="Update TRANSACTION_TASK_DETAILS record" doc:id="d3112fec-916a-425d-b3d7-7dbcf228550e" target="vUpdateDBRecordResponse" config-ref="HTTPS_Request_Transaction_DB_SYS_API" path="#[p('https.request.dbSysApi.transactionTaskDetails.path')]">
			<http:body ><![CDATA[#[output application/json
---
{
	"transactionTask": vars.vDbRecord
}]]]></http:body>
			<http:headers ><![CDATA[#[output application/java
---
vars.vRequestAttributes.'headers' default vars.vAttributes.headers]]]></http:headers>
			<http:query-params ><![CDATA[#[output application/java
---
{
	"CORRELATION_ID": vars.vDbRecord.'CORRELATION_ID',
	"DESTINATION": vars.vDbRecord.'DESTINATION'
}]]]></http:query-params>
		</http:request>
		<!-- <set-variable value="#[%dw 2.0&#10;import * from dw::core::Strings&#10;output application/json&#10;var createdDate = (&#10;	if(isEmpty(vars.vDbRecord.'createdDate')) &quot;&quot;&#10;	else &quot;CREATED_DATE = '&quot; ++ vars.vDbRecord.'createdDate' ++ &quot;', &quot;&#10;)&#10;var updateStatus = (&#10;	if(isEmpty(vars.vDbRecord.'status')) &quot;&quot;&#10;	else &quot;STATUS = '&quot; ++ vars.vDbRecord.'status' ++ &quot;', &quot;&#10;)&#10;var retryCount = (&#10;	if(isEmpty(vars.vDbRecord.'retryCount')) &quot;&quot;&#10;	else &quot;RETRY_COUNT = &quot; ++ vars.vDbRecord.'retryCount' ++ &quot;, &quot;&#10;)&#10;var errorMessage = (&#10;	if(isEmpty(vars.vDbRecord.'errorMessage')) &quot;&quot;&#10;	else &quot;ERROR_MSG = '&quot; ++ vars.vDbRecord.'errorMessage' ++ &quot;', &quot;&#10;)&#10;var errorType = (&#10;	if(isEmpty(vars.vDbRecord.'errorType')) &quot;&quot;&#10;	else &quot;ERROR_TYPE = '&quot; ++ vars.vDbRecord.'errorType' ++ &quot;', &quot;&#10;)&#10;-&#45;&#45;&#10;substringBeforeLast((&quot;UPDATE TRANSACTION_TASK_DETAILS SET &quot; ++ createdDate ++ updateStatus ++ retryCount ++ errorMessage ++ errorType), &quot;, &quot;) ++ &quot; WHERE CORRELATION_ID = '&quot; ++ vars.vDbRecord.'correlationId' ++ &quot;' AND DESTINATION = '&quot; ++ vars.vDbRecord.'destination' ++ &quot;';&quot;]" doc:name="vDynamicUpdateQuery" doc:id="2d50c2ae-c8ea-441f-85eb-58ff65c014b1" variableName="vDynamicUpdateQuery" />
		<db:update doc:name="Update TRANSACTION_TASK_DETAILS table" doc:id="005d53a8-f96c-4309-972e-3344f2a683a6" config-ref="Database_Config_Transactions">
				<db:sql><![CDATA[#[vars.vDynamicUpdateQuery]]]></db:sql>
		</db:update> -->
	</sub-flow>
</mule>
