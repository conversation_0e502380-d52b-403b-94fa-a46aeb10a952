<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:cloudhub="http://www.mulesoft.org/schema/mule/cloudhub" xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
    xmlns:tls="http://www.mulesoft.org/schema/mule/tls" xmlns:http="http://www.mulesoft.org/schema/mule/http"
    xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/tls http://www.mulesoft.org/schema/mule/tls/current/mule-tls.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/cloudhub http://www.mulesoft.org/schema/mule/cloudhub/current/mule-cloudhub.xsd">

    <error-handler name="global-error-handler">
    	<on-error-propagate type="CUSTOM:INVALID_HEADER_CONTEXT" enableNotifications="true" logException="true">
            <set-variable value='${errorCodeMessage.apikit.badRequest.code}' doc:name="httpStatus"
                doc:id="0b3d4d23-89d8-4921-9404-ff4fd152c1b4" variableName="httpStatus" />
            <set-variable value='#[{
&#10;	message: "BAD_REQUEST",
&#10;	details: error.description default ""
&#10;}]'
                doc:name="errorDescription" doc:id="dad8b56c-9d6d-4d07-aa9f-cbf9402237a7" variableName="errorDescription" />
            <flow-ref doc:name="common-error-sub-flow" doc:id="3fce7f78-a9fd-4ddf-a997-8e8206b782ef" name="common-error-sub-flow" />
        </on-error-propagate>
        <on-error-propagate type="CUSTOMNS:RECORD_DOES_NOT_EXIST" enableNotifications="true" logException="true">
            <set-variable value='${errorCodeMessage.apikit.badRequest.code}' doc:name="httpStatus"
                doc:id="4f60623c-c117-4db5-a727-cfc353b1bdf7" variableName="httpStatus" />
            <set-variable value='#[{
&#10;	message: "BAD_REQUEST",
&#10;	details: error.description default ""
&#10;}]'
                doc:name="errorDescription" doc:id="d2debc18-554a-4cf8-9311-29e04693c754" variableName="errorDescription" />
            <flow-ref doc:name="common-error-sub-flow" doc:id="a26b5bca-23de-41ce-9448-a2c4079fbba0" name="common-error-sub-flow" />
        </on-error-propagate>
        <on-error-propagate type="APIKIT:BAD_REQUEST" enableNotifications="true" logException="true">
            <set-variable value='${errorCodeMessage.apikit.badRequest.code}' doc:name="httpStatus"
                doc:id="cfe82e25-b250-4ce0-828e-82e8e5b9f9f4" variableName="httpStatus" />
            <set-variable value='#[{
&#10;	message: "BAD_REQUEST",
&#10;	details: error.description default ""
&#10;}]'
                doc:name="errorDescription" doc:id="c774173c-d934-4c0d-8def-c2ba1dba6921" variableName="errorDescription" />
            <flow-ref doc:name="common-error-sub-flow" doc:id="0dd4aae1-7d3e-4a6c-8a4c-850a82713641" name="common-error-sub-flow" />
        </on-error-propagate>
        <on-error-propagate type="APIKIT:NOT_FOUND" enableNotifications="true" logException="true">
            <set-variable value='${errorCodeMessage.apikit.notFound.code}' doc:name="httpStatus"
                doc:id="327463d4-8eb8-4f06-9e1c-21589847c01f" variableName="httpStatus" />
            <set-variable value='#[{
&#10;	message: "NOT_FOUND",
&#10;	details: error.description default ""
&#10;}]'
                doc:name="errorDescription" doc:id="4b5a6b99-19b0-4679-822a-3cf9ab7fe45e" variableName="errorDescription" />
            <flow-ref doc:name="common-error-sub-flow" doc:id="fde600b9-4c73-40a7-ab6d-27167aab67be" name="common-error-sub-flow" />
        
</on-error-propagate>
        <on-error-propagate type="APIKIT:METHOD_NOT_ALLOWED">
            <set-variable value='${errorCodeMessage.apikit.methodNotAllowed.code}' doc:name="httpStatus"
                doc:id="71949c1b-e15b-46c7-85e6-1b0e9bb93182" variableName="httpStatus" />
            <set-variable
                value="#[{
&#10;	message: &quot;METHOD_NOT_ALLOWED&quot;,
&#10;	details: p('errorCodeMessage.apikit.methodNotAllowed.description')
&#10;}]"
                doc:name="errorDescription" doc:id="d22d174e-88a3-4541-a069-ed503899971e" variableName="errorDescription" />
            <flow-ref doc:name="common-error-sub-flow" doc:id="f59fae90-28b6-4417-a3d8-5fc25964ac58" name="common-error-sub-flow" />
        </on-error-propagate>
        <on-error-propagate type="APIKIT:NOT_ACCEPTABLE" enableNotifications="true" logException="true">            
            <set-variable value='${errorCodeMessage.apikit.notAcceptable.code}' doc:name="httpStatus"
                doc:id="ac605497-a79c-44da-a494-a56777a516b6" variableName="httpStatus" />
            <set-variable
                value="#[{
&#10;	message: &quot;NOT_ACCEPTABLE&quot;,
&#10;	details: p('errorCodeMessage.apikit.notAcceptable.description')
&#10;}]"
                doc:name="errorDescription" doc:id="e8f61ee3-a828-4a4a-9d94-ea1708c708bd" variableName="errorDescription" />
            <flow-ref doc:name="common-error-sub-flow" doc:id="fa3e254b-307b-44c2-a8a6-c3b157869b85" name="common-error-sub-flow" />
        </on-error-propagate>
        <on-error-propagate type="APIKIT:UNSUPPORTED_MEDIA_TYPE">            
            <set-variable value='${errorCodeMessage.apikit.unsupportedMediaType.code}' doc:name="httpStatus"
                doc:id="dd4976f1-d27f-483f-83fa-250904e16505" variableName="httpStatus" />
            <set-variable
                value="#[{
&#10;	message: &quot;UNSUPPORTED_MEDIA_TYPE&quot;,
&#10;	details: p('errorCodeMessage.apikit.unsupportedMediaType.description')
&#10;}]"
                doc:name="errorDescription" doc:id="1fe69bdd-2b93-4a23-badb-9140b28450d0" variableName="errorDescription" />
            <flow-ref doc:name="common-error-sub-flow" doc:id="744f7ad4-a1c5-4624-af58-b7022a93c7e4" name="common-error-sub-flow" />
        </on-error-propagate>
        <on-error-propagate type="APIKIT:NOT_IMPLEMENTED" enableNotifications="true" logException="true">            
            <set-variable value='${errorCodeMessage.apikit.notImplemented.code}' doc:name="httpStatus"
                doc:id="78403814-7432-46ce-83df-2de075fd80af" variableName="httpStatus" />
            <set-variable
                value="#[{
&#10;	message: &quot;NOT_IMPLEMENTED&quot;,
&#10;	details: p('errorCodeMessage.apikit.notImplemented.description')
&#10;}]"
                doc:name="errorDescription" doc:id="0e2a24f8-1fd8-430d-84c1-dcf6e84f9286" variableName="errorDescription" />
            <flow-ref doc:name="common-error-sub-flow" doc:id="cde5de24-3357-4a18-940b-4e5482a7de5b" name="common-error-sub-flow" />
        </on-error-propagate>
        <on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate"
            doc:id="521436df-692c-48ae-8c9f-8aec14735b9e" type="EXPRESSION">            
            <set-variable value='#[attributes.statusCode default "500"]' doc:name="httpStatus"
                doc:id="98c914fa-bba4-4d54-b278-5a05ff14292b" variableName="httpStatus" />
            <set-variable
                value='#[{
&#10;	message: "DATAWEAVE_EXPRESSION_ERROR",
&#10;	details: error.description default ""
&#10;}]'
                doc:name="errorDescription" doc:id="4475c8dd-5c8a-47c5-9e07-2345e8f08032" variableName="errorDescription" />
            <flow-ref doc:name="common-error-sub-flow" doc:id="ce5c67ce-41ca-4060-93a9-fc2a0b650b8b" name="common-error-sub-flow" />
        </on-error-propagate>
        <on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate"
            doc:id="27fcef22-cc88-4fa9-becd-6169dc5f419f" type="ANY">            
            <set-variable value='#[attributes.statusCode default "500"]' doc:name="httpStatus"
                doc:id="443ad850-341a-45df-b72d-98acc04d2b6a" variableName="httpStatus" />
            <set-variable
                value='#[{
&#10;	message: "INTERNAL_SERVER_ERROR",
&#10;	details: error.description default ""
&#10;}]'
                doc:name="errorDescription" doc:id="6ba93270-6a90-4544-9fbe-e156b2d69c9e" variableName="errorDescription" />
            <flow-ref doc:name="common-error-sub-flow" doc:id="12f5010c-23b2-467e-a272-c74e4e87bfbf" name="common-error-sub-flow" />
        </on-error-propagate>
    </error-handler>
    <sub-flow name="common-error-sub-flow" doc:id="588ae881-c4ee-46e0-9173-7251a11ce22d">
		<set-payload
            value='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	code: vars.httpStatus,&#10;	status: "FAILURE",&#10;	transactionId: vars.vCorrelationId,&#10;	response: {&#10;		message: vars.errorDescription.message,&#10;		details: vars.errorDescription.details&#10;	}&#10;}]'
            doc:name="Final Error Response" doc:id="5dadb469-5c4d-48b3-8862-74c34e91c61e" />
        <logger level="ERROR" doc:name="Exit Generic Error Handler" doc:id="40d6d840-d6f9-4414-a4aa-5cb3d37ff6c2"
            message='#[payload]' />
    </sub-flow>
</mule>
