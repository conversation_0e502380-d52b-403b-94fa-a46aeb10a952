<?xml version="1.0" encoding="UTF-8"?>

<mule
	xmlns:apikit="http://www.mulesoft.org/schema/mule/mule-apikit"
	xmlns:api-gateway="http://www.mulesoft.org/schema/mule/api-gateway"
	xmlns:tls="http://www.mulesoft.org/schema/mule/tls"
	xmlns:netsuite="http://www.mulesoft.org/schema/mule/netsuite"
	xmlns:db="http://www.mulesoft.org/schema/mule/db"
	xmlns:secure-properties="http://www.mulesoft.org/schema/mule/secure-properties"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:salesforce="http://www.mulesoft.org/schema/mule/salesforce"
	xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/mule-apikit http://www.mulesoft.org/schema/mule/mule-apikit/current/mule-apikit.xsd 
http://www.mulesoft.org/schema/mule/db http://www.mulesoft.org/schema/mule/db/current/mule-db.xsd http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/salesforce http://www.mulesoft.org/schema/mule/salesforce/current/mule-salesforce.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/secure-properties http://www.mulesoft.org/schema/mule/secure-properties/current/mule-secure-properties.xsd
http://www.mulesoft.org/schema/mule/netsuite http://www.mulesoft.org/schema/mule/netsuite/current/mule-netsuite.xsd
http://www.mulesoft.org/schema/mule/tls http://www.mulesoft.org/schema/mule/tls/current/mule-tls.xsd
http://www.mulesoft.org/schema/mule/api-gateway http://www.mulesoft.org/schema/mule/api-gateway/current/mule-api-gateway.xsd">
	
	<http:listener-config name="HTTPS_Listener_config" doc:name="HTTP Listener config" doc:id="b95d0462-04eb-48c9-9008-f69c9357435a">
        <http:listener-connection host="${https.listener.host}" port="${https.listener.port}" readTimeout="${https.listener.readTimeout}" connectionIdleTimeout="${https.listener.idleTimeout}" protocol="HTTPS" tlsContext="TLS_Context_Inbound" />
    </http:listener-config>
    <apikit:config name="3degreesNetsuiteSysAPI-config" api="resource::a970b687-ceb1-48a0-9bc7-6fed0e331363:3degrees-netsuite-sys-api:1.0.58:raml:zip:3degreesNetsuiteSysAPI.raml" outboundHeadersMapName="outboundHeaders" httpStatusVarName="httpStatus" />
    
    <tls:context name="TLS_Context_Inbound"
		doc:name="TLS Context" doc:id="2c328b8f-bfef-4123-aa56-b0b387c53542">
		<tls:key-store type="jks"
			path="${https.listener.keystore.path}"
			keyPassword="${secure::https.listener.keystore.keyPassword}"
			password="${secure::https.listener.keystore.password}" />
	</tls:context>
	<tls:context name="TLS_Context_Transaction_DB_Outbound"
		doc:name="TLS Context" doc:id="53567415-3b3c-4576-bd50-c229542343ab">
		<tls:trust-store
			path="${https.request.transactionDBSysApi.truststore.path}"
			password="${secure::https.request.transactionDBSysApi.truststore.password}"
			type="jks" />
	</tls:context>

	
	<configuration-properties
		doc:name="Configuration properties"
		doc:id="6078461f-a4cb-46c9-815f-eeffcbc9a1bd"
		file="config\config-common.properties" />
	<configuration-properties
		doc:name="Configuration properties"
		doc:id="a852110a-5495-4b3c-a619-69c88a141470"
		file="config\config-${mule.env}.properties" />
	<secure-properties:config
		name="Secure_Properties_Config" doc:name="Secure Properties Config"
		doc:id="b839631a-8668-4e1c-a237-8c6c0d4d7f66"
		file="config\config-${mule.env}-secure.properties" key="${mule.key}">
		<secure-properties:encrypt
			algorithm="Blowfish" />
	</secure-properties:config>

	<!-- <db:config name="Database_Config_Transactions" doc:name="Database Config" 
		doc:id="9d05e295-1722-4767-8c0d-fa7196b5f91d" > <db:my-sql-connection host="${secure::db.host}" 
		port="${db.port}" user="${secure::db.username}" database="${secure::db.database}" 
		/> </db:config> -->
	<netsuite:config name="NetSuite_Config"
		doc:name="NetSuite Config"
		doc:id="e0efd821-897d-426c-8c24-4645d872af8f" runServerSuiteScriptAndTriggerWorkflows="true">
		<netsuite:token-based-authentication-connection
			consumerKey="${secure::netsuite.consumerKey}"
			consumerSecret="${secure::netsuite.consumerSecret}"
			tokenId="${secure::netsuite.tokenId}"
			tokenSecret="${secure::netsuite.tokenSecret}"
			account="${secure::netsuite.accountId}"
			wsdlVersion="#[Mule::p('netsuite.wsdlVersion')]"
			soapPort="#[Mule::p('netsuite.soapPort')]"
			signatureAlgorithm="#[Mule::p('netsuite.signatureAlgorithm')]" readTimeout="${netsuite.readTimeout}" connectionTimeout="${netsuite.connectionTimeout}">
			<reconnection failsDeployment="true" >
				<reconnect frequency="${netsuite.reconnection.frequency}" count="${netsuite.reconnection.attempts}" />
			</reconnection>
		</netsuite:token-based-authentication-connection>
	</netsuite:config>
	<http:request-config
		name="HTTPS_Request_Transaction_DB_SYS_API"
		doc:name="HTTP Request configuration"
		doc:id="6b30017d-960d-475d-8e3d-ba61793de553">
		<http:request-connection
			host="${https.request.transactionDBSysApi.host}" protocol="HTTPS"
			port="${https.request.transactionDBSysApi.port}"
			connectionIdleTimeout="${https.request.transactionDBSysApi.connectionTimeout}" tlsContext="TLS_Context_Transaction_DB_Outbound">
			<reconnection>
				<reconnect
					frequency="${https.request.transactionDBSysApi.reconnection.frequency}"
					count="${https.request.transactionDBSysApi.reconnection.attempts}" />
			</reconnection>
		</http:request-connection>
		<http:default-headers>
			<http:default-header key="client_id"
				value="#[p('secure::https.request.transactionDBSysApi.headers.clientId')]" />
			<http:default-header key="client_secret"
				value="#[p('secure::https.request.transactionDBSysApi.headers.clientSecret')]" />
		</http:default-headers>
	</http:request-config>

	<api-gateway:autodiscovery
		apiId="${api.autodiscoveryId}" ignoreBasePath="true"
		doc:name="API Autodiscovery"
		doc:id="f2c3bbc8-2ac5-4856-bb64-64ed71d7873e"
		flowRef="3degreesNetsuiteSysAPI-main" />
</mule>