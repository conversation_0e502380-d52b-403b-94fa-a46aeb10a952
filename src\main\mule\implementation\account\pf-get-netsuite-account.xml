<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:netsuite="http://www.mulesoft.org/schema/mule/netsuite"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/netsuite http://www.mulesoft.org/schema/mule/netsuite/current/mule-netsuite.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd">
	<flow name="pf-get-netsuite-account"
		doc:id="2f2f005a-a798-4577-9ee9-c68e00d92fe5">
		<logger level="INFO" doc:name="LOG INFO: Log Entry"
			doc:id="28d3a3b8-08f4-400e-a839-f8b722454ec6"
			message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-get-netsuite-account", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request"
			doc:id="bd21621c-19fb-4638-806b-fa9e46bdb239"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "pf-get-netsuite-account",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/account",&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Request Payload"
			doc:id="0389b40d-9f48-406e-9791-f1588d314c0f"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "pf-get-netsuite-account",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/account",&#10;	"BackendRequest": payload,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<ee:transform doc:name="Set vInternalId, vEnterpriseId, vRequestAttributes"
			doc:id="1d94fb3e-994b-4241-9864-6b1c8b5154ab">
			<ee:message />
			<ee:variables>
				<ee:set-variable variableName="vInternalId"><![CDATA[%dw 2.0
output application/json
---
attributes.queryParams.'internalId' default ""]]></ee:set-variable>
				<ee:set-variable variableName="vEnterpriseId" ><![CDATA[%dw 2.0
output application/json
---
attributes.queryParams.'eid' default ""]]></ee:set-variable>
				<ee:set-variable variableName="vRequestAttributes" ><![CDATA[%dw 2.0
output application/json
---
{
	"headers": {
		"x-source": vars.vAttributes.headers.'x-source' default "",
		"x-transactionId": vars.vTransactionId,
		"x-msg-timestamp": now() as DateTime,
		"correlationId": vars.vCorrelationId,
		"sourceId": "NETSUITE_SYS_API",
		"destinationId": "TRANSACTION_DB_SYS_API",
		"content-type": "application/json"
	}
}]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<choice doc:name="Check if vInternalId is empty"
			doc:id="3e6c8c0d-d4da-404e-b869-6ef160c308ee">
			<when expression="#[isEmpty(vars.vInternalId)]">
				<flow-ref
					doc:name="Flow Reference to sf-get-netsuite-internal-id"
					doc:id="7d6fd1fc-29fa-439c-9141-b3834380ecb7"
					name="sf-get-netsuite-internal-id" target="vInternalId" />
			</when>
			<otherwise >
				<flow-ref doc:name="Flow Reference to sf-get-netsuite-enterprise-id" doc:id="a0a1231c-2f23-444e-a7c4-a4f6836113bb" name="sf-get-netsuite-enterprise-id" target="vEnterpriseId" />
			</otherwise>
		</choice>
		<try doc:name="Try" doc:id="d3afff1a-ee3e-469b-ad8e-85d78d87f377" >
			<flow-ref doc:name="Flow Reference to sf-retrieve-netsuite-account" doc:id="2b66ea23-1d0e-4b36-9c47-028cb4b0f989" name="sf-retrieve-netsuite-account" target="vRetrieveCustomerResponse" />
			<error-handler >
				<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="6ed394e4-e62e-4761-809b-d023c6edaf77" type="CUSTOMNS:RECORD_DOES_NOT_EXIST">
					<set-variable value="#[output application/json&#10;---&#10;{&#10;	&quot;getResponse&quot;: {&#10;		&quot;status&quot;: &quot;FAILURE&quot;,&#10;		&quot;detail&quot;: &quot;Record with internalid &quot; ++ vars.vInternalId ++ &quot; doesn't exist in Netsuite&quot;&#10;	}&#10;}]" doc:name="vRetrieveCustomerResponse" doc:id="f6beeeb6-d5b6-4770-990b-22159f9f1981" variableName="vRetrieveCustomerResponse"/>
				</on-error-continue>
			</error-handler>
		</try>
		<ee:transform doc:name="Set payload, httpStatus" doc:id="9472705d-b06d-4dcc-91b7-6e535ade1f7b" >
			<ee:message >
				<ee:set-payload ><![CDATA[%dw 2.0
import substringBeforeLast from dw::core::Strings
var accountRecord = vars.vRetrieveCustomerResponse.getResponse.readResponse.record default {}
var addressBook = accountRecord.addressbookList.addressbook.addressbookAddress default {}
fun getUsernameFromEmailAddress(email) = (
	substringBeforeLast((email default ""), "@")
)
var modifiedByEmailAddress = accountRecord.customFieldList.StringCustomFieldRef__custentity_last_modified_by.value
output application/json
---
if(!isEmpty(accountRecord)) {
	"code": 200,
    "status": "SUCCESS",
    "transactionId": vars.vTransactionId,
	"response": {
		"account": {
		    "companyname": accountRecord.companyName default null,
		    "accountupdatedby": (
		    	if((isEmpty(modifiedByEmailAddress)) or (lower(modifiedByEmailAddress) ~= Mule::p('netsuite.user.mulesoft.emailAddress')))
		    		accountRecord.customFieldList.StringCustomFieldRef__custentity_accountupdatedby.value default null
		    	else
		    		("NS" ++ "\\" ++ (accountRecord.customFieldList.StringCustomFieldRef__custentity_last_modified_by.'value' default "")) default null
		    ),
		    "isperson": accountRecord.isPerson default null,
		    "entitystatus": accountRecord.entityStatus.name default null,
		    "subsidiary": accountRecord.subsidiary.name default null,
		    "customform": accountRecord.customForm.name default null,
		    "addr1": addressBook.addr1 default null,
		    "addr2": addressBook.addr2 default null,
		    "city": addressBook.city default null,
		    "state": addressBook.state default null,
		    "zip": addressBook.zip default null,
		    "country": addressBook.country default null,
		    "internalid": vars.vInternalId default null,
			"candelete": accountRecord.customFieldList.BooleanCustomFieldRef__custentity_candelete.value default null,
		    "eid": vars.vEnterpriseId default null,
			"syncStatus": accountRecord.customFieldList.SelectCustomFieldRef__custentity_customersyncmessage.value.name default null,
			"syncMessage": accountRecord.customFieldList.StringCustomFieldRef__custentity_syncmessage.value default null
		}
	}
}
else {
    "code": 200,
    "status": "SUCCESS",
    "transactionId": vars.vTransactionId,
    "response": {
      "message": "RECORD_NOT_FOUND",
      "details": vars.vRetrieveCustomerResponse.getResponse.detail
    }
}]]></ee:set-payload>
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="httpStatus" ><![CDATA[200]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Response Payload"
			doc:id="47f9e2dc-4389-48de-8349-9bbf4f680263"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-get-netsuite-account",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/account",&#10;	"BackendResponse": payload,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response"
			doc:id="04e50be5-9a16-4332-9d79-80445185bd03"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-get-netsuite-account",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "/api/account",&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit"
			doc:id="e4e3ba6f-a648-4e38-8bd0-402f7bf7227e"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-get-netsuite-account",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />

	</flow>
	<sub-flow name="sf-retrieve-netsuite-account" doc:id="de819fb4-35cb-4f71-9216-7a7d9a5625d3" >
		<ee:transform doc:name="Set vRetrieveCustomerBody" doc:id="f35c3720-89b9-4580-902a-691abe288703">
			<ee:message>
			</ee:message>
			<ee:variables>
				<ee:set-variable resource="dwl/read/readNetsuiteAccountMapping.dwl" variableName="vRetrieveCustomerBody" />
			</ee:variables>
		</ee:transform>
		<netsuite:get doc:name="Retrieve Customer data" doc:id="cedb358a-b798-40eb-813b-1530854b45d5" config-ref="NetSuite_Config" refType="RecordRef" type="customer">
			<error-mapping sourceType="NETSUITE:NETSUITE_ERROR" targetType="CUSTOMNS:RECORD_DOES_NOT_EXIST" />
			<reconnect frequency="${netsuite.retry.timePeriod}" count="${netsuite.retry.maxRetries}" />
			<netsuite:message><![CDATA[#[vars.vRetrieveCustomerBody]]]></netsuite:message>
		</netsuite:get>
	</sub-flow>
</mule>
