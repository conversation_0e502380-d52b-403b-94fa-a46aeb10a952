<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:netsuite="http://www.mulesoft.org/schema/mule/netsuite"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/netsuite http://www.mulesoft.org/schema/mule/netsuite/current/mule-netsuite.xsd">
	<flow name="pf-update-netsuite-account"
		doc:id="5e872c2b-e452-4b63-9080-f2bf7f55b220">
		<logger level="INFO" doc:name="LOG INFO: Log Entry"
			doc:id="a76dba84-5490-47ce-9c20-cbe08720f035"
			message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-update-netsuite-account", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request"
			doc:id="1fb724b8-2aee-4777-bdda-0d676957811a"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "pf-update-netsuite-account",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/account",&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Request Payload"
			doc:id="d588a7d9-0944-47d4-a3ef-432053dbaef2"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "pf-update-netsuite-account",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/account",&#10;	"BackendRequest": payload,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<ee:transform doc:name="Set vDbRecord, vRequestAttributes" doc:id="0f7ee9f1-904f-468e-8021-7f68dec6dbcf">
			<ee:message>
			</ee:message>
			<ee:variables>
				<ee:set-variable variableName="vDbRecord"><![CDATA[%dw 2.0
import * from dw::core::Strings
import * from dw::core::Arrays
output application/json
---
{
	"CORRELATION_ID": vars.vCorrelationId,
	"OBJECT_TYPE": "ACCOUNT",
	"OPERATION": "UPDATE",
	"SOURCE": vars.vAttributes.headers.'x-source' default null,
	"TASK_PAYLOAD": write(payload, "application/json") default null,
	"DESTINATION": "NETSUITE",
	"STATUS": "IN_PROGRESS",
	"RETRY_COUNT": 0,
	"LAST_UPDATED_BY": "SYSTEM_API",
	"QUERY_PARAMS": ((((vars.vAttributes.queryParams mapObject() -> {
	    "key": ($$),
	    "val": ($)
	}) default {} pluck($) divideBy(2)) map() -> (($) joinBy "|")) joinBy "||") default null,
	"ERROR_MSG": null,
    "ERROR_TYPE": null
}]]></ee:set-variable>
				<ee:set-variable variableName="vRequestAttributes"><![CDATA[%dw 2.0
output application/json
---
{
	"headers": {
		"x-source": vars.vAttributes.headers.'x-source' default "",
		"x-transactionId": vars.vTransactionId,
		"x-msg-timestamp": now() as DateTime,
		"correlationId": vars.vCorrelationId,
		"sourceId": "NETSUITE_SYS_API",
		"destinationId": "TRANSACTION_DB_SYS_API",
		"content-type": "application/json"
	}
}]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<ee:transform doc:name="Set vInternalId, vEnterpriseId"
			doc:id="4a830b51-5b4c-4535-a6e5-16aef8f5c061">
			<ee:message>
			</ee:message>
			<ee:variables>
				<ee:set-variable variableName="vInternalId"><![CDATA[%dw 2.0
output application/json
---
attributes.queryParams.'internalId' default ""]]></ee:set-variable>
				<ee:set-variable variableName="vEnterpriseId" ><![CDATA[%dw 2.0
output application/json
---
attributes.queryParams.'eid' default ""]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<choice doc:name="Check header context" doc:id="d5e1bc24-a471-4795-bca6-9e3c25bf9314">
			<when expression="#[(vars.vAttributes.headers.'context' ~= &quot;REGULAR&quot;)]">
				<flow-ref doc:name="Flow Reference to sf-insert-into-transaction-task-details-table" doc:id="65a2ee84-de12-4690-9a66-37ee1edf51a6" name="sf-insert-into-transaction-task-details-table" />
			</when>
			<when expression="#[((vars.vAttributes.headers.'context' ~= &quot;ROLLBACK&quot;) or (vars.vAttributes.headers.'context' ~= &quot;RETRY&quot;) or (vars.vAttributes.headers.'context' ~= &quot;PARTIAL_UPDATE&quot;))]">
				<logger level="INFO" doc:name="LOG INFO: Skip STATUS update" doc:id="aa88329a-6a41-4bf2-8b80-e9a3968eeee8" message="#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	&quot;Message&quot; : &quot;No update is made to TRANSACTION_TASK_DETAILS table as headers context is &quot; ++ (vars.vAttributes.headers.'context' default &quot;&quot;),&#10;	&quot;FlowName&quot; : &quot;pf-update-netsuite-account&quot;,&#10;	&quot;CorrelationID&quot; : vars.vCorrelationId,&#10;	&quot;BusinessKey&quot; : vars.vBusinessKey&#10;}]" />
			</when>
			<otherwise>
				<raise-error doc:name="CUSTOM:INVALID_HEADER_CONTEXT" doc:id="55118a0c-91ac-44f5-af83-b81999a2049e" type="CUSTOM:INVALID_HEADER_CONTEXT" description="#[(&quot;Invalid value for context &quot; ++ (vars.vAttributes.headers.'context' default &quot;null&quot;) ++ &quot; is passed&quot;)]" />
			</otherwise>
		</choice>
		<choice doc:name="Check if vInternalId is empty"
			doc:id="d1852ce5-b7a4-41bf-87c9-5e4be15a53ef">
			<when expression="#[isEmpty(vars.vInternalId)]">
				<flow-ref
					doc:name="Flow Reference to sf-get-netsuite-internal-id"
					doc:id="791de889-0f46-4aec-b7e2-7d02d7b65a24"
					name="sf-get-netsuite-internal-id" target="vInternalId" />
			</when>
			<otherwise >
				<flow-ref doc:name="Flow Reference to sf-get-netsuite-enterprise-id" doc:id="7930792a-835b-41ac-b27b-6acf1bae4ec6" name="sf-get-netsuite-enterprise-id" target="vEnterpriseId"/>
			</otherwise>
		</choice>

		<flow-ref doc:name="Flow Reference sf-retrieve-netsuite-account" doc:id="fc518171-c2ae-40a5-86d1-bd687b0e8a04" target="vRetrieveCustomerResponse" name="sf-retrieve-netsuite-account"/>
		<ee:transform doc:name="Set vUpdateCustomerBody"
			doc:id="6974b0d8-faa5-46be-87d2-b6eaee3d77de">
			<ee:message>
			</ee:message>
			<ee:variables >
				<ee:set-variable resource="dwl/update/updateNetsuiteAccountMapping.dwl" variableName="vUpdateCustomerBody" />
			</ee:variables>
		</ee:transform>
		<try doc:name="Try" doc:id="03408d3a-a2b7-4a31-bfc8-daee2450941e">
			<set-variable value='#["0" as Number]'
				doc:name="vRetryCount" doc:id="f4cd8656-b2aa-41eb-97dd-9717c69b58d5"
				variableName="vRetryCount" />
			<until-successful
				maxRetries="${netsuite.retry.maxRetries}" doc:name="Until Successful"
				doc:id="062c2c91-b98f-4a35-b433-a437dbb78609"
				millisBetweenRetries="${netsuite.retry.timePeriod}">
				<try doc:name="Try"
					doc:id="92916a17-ca42-41ce-a5c2-ed57dbc8c89e">
					<netsuite:update doc:name="Update Customer" doc:id="4fa61162-ec37-4c7b-aca8-c7a891d6ea34" config-ref="NetSuite_Config" type="Customer" target="vUpdateCustomerResponse">
						<netsuite:message ><![CDATA[#[vars.vUpdateCustomerBody]]]></netsuite:message>
					</netsuite:update>
					<ee:transform doc:name="Update vDbRecord, vRequestAttributes" doc:id="5b3681e7-215d-4f04-8072-d07cf4c365e4" >
						<ee:message >
						</ee:message>
						<ee:variables >
							<ee:set-variable variableName="vDbRecord" ><![CDATA[%dw 2.0
import * from dw::core::Strings
output application/json
---
vars.vDbRecord update {
	case STATUS at .STATUS -> "COMPLETED"
    case RETRY_COUNT at .RETRY_COUNT -> vars.vRetryCount as Number   
}]]></ee:set-variable>
							<ee:set-variable variableName="vRequestAttributes" ><![CDATA[%dw 2.0
output application/json
---
vars.vRequestAttributes update {
	case timestamp at .headers.'x-msg-timestamp' -> now()
}]]></ee:set-variable>
						</ee:variables>
					</ee:transform>
					<error-handler>
						<on-error-propagate enableNotifications="true"
							logException="true" doc:name="On Error Propagate"
							doc:id="1b32ef14-404b-4b7a-a6dc-166b7ac87f93"
							type="NETSUITE:CONNECTIVITY, NETSUITE:SESSION_TIMED_OUT">
							<set-variable
								value="#[(vars.vRetryCount as Number) + 1]"
								doc:name="Update vRetryCount"
								doc:id="d36c54ea-670c-4d8d-9be2-934baba78afd"
								variableName="vRetryCount" />
						</on-error-propagate>
						<on-error-continue enableNotifications="true"
							logException="true" doc:name="On Error Continue"
							doc:id="9641362b-19ca-4c4e-b60e-212cead3e996"
							type="NETSUITE:INVALID_VERSION, NETSUITE:NETSUITE_ERROR, NETSUITE:NETSUITE_SOAP_FAULT, NETSUITE:USER_ERROR">
							<ee:transform doc:name="Update vDbRecord, vRequestAttributes" doc:id="3e677b77-ed15-4fac-8624-62d5524bac7f" >
								<ee:message >
								</ee:message>
								<ee:variables >
									<ee:set-variable variableName="vDbRecord" ><![CDATA[output application/json
---
vars.vDbRecord update {
	case STATUS at .STATUS -> "FAILED"
    case RETRY_COUNT at .RETRY_COUNT -> vars.vRetryCount as Number   
    case ERROR_MSG at .ERROR_MSG -> error.description replace "\"" with ""
    case ERROR_TYPE at .ERROR_TYPE -> "DATA"
}]]></ee:set-variable>
									<ee:set-variable variableName="vRequestAttributes" ><![CDATA[%dw 2.0
output application/json
---
vars.vRequestAttributes update {
	case timestamp at .headers.'x-msg-timestamp' -> now()
}]]></ee:set-variable>
								</ee:variables>
							</ee:transform>
						</on-error-continue>
					</error-handler>
				</try>
			</until-successful>
			<choice doc:name="Check header context" doc:id="e08afc94-7996-4273-b05f-eb4d0ba401dd" >
				<when expression="#[(vars.vAttributes.headers.'context' ~= &quot;REGULAR&quot;)]">
					<flow-ref doc:name="Flow Reference to sf-update-transaction-task-details-table" doc:id="c99c3c08-81e9-4181-a2f5-bbdaaae7a04a" name="sf-update-transaction-task-details-table" />
				</when>
			</choice>
			<ee:transform doc:name="Set payload, httpStatus" doc:id="53ddeaaa-9ea6-4e88-8fe5-710671967548" >
				<ee:message >
					<ee:set-payload ><![CDATA[output application/json
var updateStatus = ((vars.vDbRecord.'STATUS' ~= "COMPLETED") and (vars.vUpdateCustomerResponse.updateResponse.writeResponse.status.@isSuccess ~= "true"))
---
if(updateStatus) {
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "SUCCESS",
  "response": {
    "internalid": vars.vInternalId default "",
    "eid": vars.vEnterpriseId default ""
  }
}
else  {
    "code": 400,
    "status": "FAILURE",
    "transactionId": vars.vTransactionId,
    "response": {
      "message": vars.vDbRecord.'ERROR_TYPE',
      "details": vars.vDbRecord.'ERROR_MSG'
    }
}]]></ee:set-payload>
				</ee:message>
				<ee:variables >
					<ee:set-variable variableName="httpStatus" ><![CDATA[output application/json
var updateStatus = vars.vUpdateCustomerResponse.updateResponse.writeResponse.status.@isSuccess ~= "true"
---
if(updateStatus default false) 200 else 400]]></ee:set-variable>
				</ee:variables>
			</ee:transform>
			<error-handler>
				<on-error-propagate enableNotifications="true"
					logException="true" doc:name="On Error Propagate"
					doc:id="e5e8690f-4ac6-4621-a08e-3eeb6beb09e9" type="ANY">
					<ee:transform doc:name="Update vDbRecord, vRequestAttributes" doc:id="2ed540df-80a0-4da7-92a1-80b002c0ec8e">
						<ee:message />
						<ee:variables>
							<ee:set-variable variableName="vDbRecord"><![CDATA[output application/json
---
vars.vDbRecord update {
    case STATUS at .status -> "FAILED"
    case RETRY_COUNT at .retryCount -> p('netsuite.retry.maxRetries') as String
    case ERROR_MSG at .ERROR_MSG -> error.description replace "\"" with ""
    case ERROR_TYPE at .ERROR_TYPE -> "SYSTEM"
}]]></ee:set-variable>
							<ee:set-variable variableName="vRequestAttributes"><![CDATA[%dw 2.0
output application/json
---
vars.vRequestAttributes update {
	case timestamp at .headers.'x-msg-timestamp' -> now()
}]]></ee:set-variable>
						</ee:variables>
					</ee:transform>
					<flow-ref
						doc:name="Flow Reference to sf-update-transaction-task-details-table"
						doc:id="31928eb0-6a72-4499-b607-b4ec5bb51f15"
						name="sf-update-transaction-task-details-table" />
				</on-error-propagate>
			</error-handler>
		</try>
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Response Payload"
			doc:id="eea7c2b8-aef2-48bb-8711-486ce5891032"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-update-netsuite-account",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/account",&#10;	"BackendResponse": payload,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response"
			doc:id="53f2adde-f311-4bcb-a5d4-224b2a836cd7"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-update-netsuite-account",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "/api/account",&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit"
			doc:id="7ab0ad2f-940f-4a92-b1b5-cecdd1a5e6fe"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;  	"Message": "Flow Ended",&#10;	"FlowName": "pf-update-netsuite-account",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
	</flow>
</mule>
