<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:netsuite="http://www.mulesoft.org/schema/mule/netsuite"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/netsuite http://www.mulesoft.org/schema/mule/netsuite/current/mule-netsuite.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd">
	<flow name="pf-get-netsuite-invoice"
		doc:id="2f2f005a-a798-4577-9ee9-c68e00d92fe5">
		<logger level="INFO" doc:name="LOG INFO: Log Entry"
			doc:id="28d3a3b8-08f4-400e-a839-f8b722454ec6"
			message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-get-netsuite-invoice", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request"
			doc:id="bd21621c-19fb-4638-806b-fa9e46bdb239"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "pf-get-netsuite-invoice",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/invoices",&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<ee:transform doc:name="Set vInternalId"
			doc:id="1d94fb3e-994b-4241-9864-6b1c8b5154ab">
			<ee:message />
			<ee:variables>
				<ee:set-variable variableName="vInternalId"><![CDATA[%dw 2.0
output application/json
---
attributes.queryParams.'invoiceNSId' ]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Request Payload" doc:id="0389b40d-9f48-406e-9791-f1588d314c0f" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;  	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "pf-get-netsuite-invoice",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/invoices",&#10;	"QueryParameter": vars.vInternalId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<try doc:name="Try" doc:id="d3afff1a-ee3e-469b-ad8e-85d78d87f377" >
			<flow-ref doc:name="Flow Reference to sf-retrieve-netsuite-invoice" doc:id="2b66ea23-1d0e-4b36-9c47-028cb4b0f989" name="sf-retrieve-netsuite-invoice" target="vRetrieveInvoiceResponse" />
			<error-handler >
				<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="6ed394e4-e62e-4761-809b-d023c6edaf77" type="CUSTOMNS:RECORD_DOES_NOT_EXIST">
					<ee:transform doc:name="set vRetrieveInvoiceResponse" doc:id="871f1548-ea88-4865-b2ac-190413c1a56f" >
						<ee:message >
						</ee:message>
						<ee:variables >
							<ee:set-variable variableName="vRetrieveInvoiceResponse" ><![CDATA[output application/json
---
{  
    "ERROR_MSG": error.description replace "\"" with "",
    "ERROR_TYPE": error.errorType.identifier
}]]></ee:set-variable>
						</ee:variables>
					</ee:transform>
				</on-error-continue>
			</error-handler>
		</try>
		<ee:transform doc:name="Set payload" doc:id="9472705d-b06d-4dcc-91b7-6e535ade1f7b" >
			<ee:message >
				<ee:set-payload ><![CDATA[%dw 2.0
import substringBeforeLast from dw::core::Strings
var invoiceRecord = vars.vRetrieveInvoiceResponse.getResponse.readResponse.record default {}

output application/json skipNullOn="everywhere"
---
if(!isEmpty(invoiceRecord)) {
	"code": 200,
    "status": "SUCCESS",
    "transactionId": vars.vTransactionId,
	"response": {
		"invoice": {
            "sfId": if (invoiceRecord.customFieldList.StringCustomFieldRef__custbody_salesforce_id.value != null) (invoiceRecord.customFieldList.StringCustomFieldRef__custbody_salesforce_id.value)  else null,
		    "netSuiteInvoiceId": if (invoiceRecord.@internalId != null) (invoiceRecord.@internalId as Number) else null,
		    "entity": if (invoiceRecord.entity.name != null) invoiceRecord.entity.name else null,
		    "amountPaid": if (invoiceRecord.amountPaid != null) (invoiceRecord.amountPaid as Number) else null,
		    "amountRemaining": if (invoiceRecord.amountRemaining != null) (invoiceRecord.amountRemaining as Number) else null,
		    "tranId": if (invoiceRecord.tranId != null) invoiceRecord.tranId else null,
		    "currency": if (invoiceRecord.currency.@internalId != null) invoiceRecord.currency.@internalId else null,
		    "approvalStatus": if (invoiceRecord.approvalStatus.@internalId != null) invoiceRecord.approvalStatus.@internalId else null,
		    "postingPeriod": if (invoiceRecord.postingPeriod.name != null) invoiceRecord.postingPeriod.name else null,
		    "subtotal": if (invoiceRecord.subTotal != null) (invoiceRecord.subTotal as Number) else null,
		    "taxTotal": if (invoiceRecord.taxTotal != null) (invoiceRecord.taxTotal as Number) else null,
		    "total": if (invoiceRecord.total != null) (invoiceRecord.total as Number) else null,
		    "terms": if (invoiceRecord.terms.@internalId != null) invoiceRecord.terms.@internalId else null,
		    "dueDate": if (invoiceRecord.dueDate != null) invoiceRecord.dueDate else null,
		    "tranDate": if (invoiceRecord.tranDate != null) invoiceRecord.tranDate else null,
		    "fullfillmentModel": if (invoiceRecord.customFieldList.SelectCustomFieldRef__custbody_fulfillment_model.value.@internalId != null) (invoiceRecord.customFieldList.SelectCustomFieldRef__custbody_fulfillment_model.value.@internalId) else null,
		    "salesforceOrderNumer": if (invoiceRecord.customFieldList.LongCustomFieldRef__custbody_salesforce_order_number.value != null) (invoiceRecord.customFieldList.LongCustomFieldRef__custbody_salesforce_order_number.value as Number) else null,
		    "status": if (invoiceRecord.status != null) invoiceRecord.status else null,
		    "resyncComplete": if (invoiceRecord.customFieldList.BooleanCustomFieldRef__custbodyresynccomplete.value != null) (invoiceRecord.customFieldList.BooleanCustomFieldRef__custbodyresynccomplete.value) else null,
		    "resyncToSF": if (invoiceRecord.customFieldList.BooleanCustomFieldRef__custbody_resync.value != null) (invoiceRecord.customFieldList.BooleanCustomFieldRef__custbody_resync.value) else null,
		    "comments": if (invoiceRecord.customFieldList.StringCustomFieldRef__custbody_commentsfromsf.value != null) (invoiceRecord.customFieldList.StringCustomFieldRef__custbody_commentsfromsf.value) else null,
		    "invoiceSynctoSF": if (invoiceRecord.customFieldList.StringCustomFieldRef__custbody_invoicesynctosf.value != null) (invoiceRecord.customFieldList.StringCustomFieldRef__custbody_invoicesynctosf.value) else null,
		    "statusSync": if (invoiceRecord.customFieldList.SelectCustomFieldRef__custbody_statussync.value.name != null) invoiceRecord.customFieldList.SelectCustomFieldRef__custbody_statussync.value.name else null,
		    "invoiceSynctoArtemis": if (invoiceRecord.customFieldList.StringCustomFieldRef__custbody_invoicesynctoartemis.value != null) (invoiceRecord.customFieldList.StringCustomFieldRef__custbody_invoicesynctoartemis.value) else null,
		    "lastPaymentDate": if(!isEmpty(invoiceRecord.customFieldList.DateCustomFieldRef__custbody_latestpaymentdate)) invoiceRecord.customFieldList.DateCustomFieldRef__custbody_latestpaymentdate.value else null,
		    "clientAmountPaid": if(!isEmpty(invoiceRecord.customFieldList.DoubleCustomFieldRef__custbody_clientamountpaid.value)) invoiceRecord.customFieldList.DoubleCustomFieldRef__custbody_clientamountpaid.value as Number else null,
		    "3dCreditedAmount": if(!isEmpty(invoiceRecord.customFieldList.DoubleCustomFieldRef__custbody_3dcreditedamount.value)) invoiceRecord.customFieldList.DoubleCustomFieldRef__custbody_3dcreditedamount.value as Number else null,
		    "statusSynctoArtemis": if (invoiceRecord.customFieldList.SelectCustomFieldRef__custbody_statussynctoartemis.value.name != null) invoiceRecord.customFieldList.SelectCustomFieldRef__custbody_statussynctoartemis.value.name else null,
		//new fields    
		    "commoditiesSold": if (invoiceRecord.customFieldList.BooleanCustomFieldRef__custbody_commodities_sold.value != null) invoiceRecord.customFieldList.BooleanCustomFieldRef__custbody_commodities_sold.value else null,
	        "lastModifiedBy": if (invoiceRecord.customFieldList.StringCustomFieldRef__custbody_lastmodifiedby.value != null) invoiceRecord.customFieldList.StringCustomFieldRef__custbody_lastmodifiedby.value else null,
	        "artemisInvoiceId": if (invoiceRecord.customFieldList.StringCustomFieldRef__custbody_artemisinvoiceid.value != null) invoiceRecord.customFieldList.StringCustomFieldRef__custbody_artemisinvoiceid.value else null,
	        "3dConfirmId": if (invoiceRecord.customFieldList.StringCustomFieldRef__custbody_3d_confirm_id.value != null) invoiceRecord.customFieldList.StringCustomFieldRef__custbody_3d_confirm_id.value else null,
	        "createdBy": if (invoiceRecord.customFieldList.StringCustomFieldRef__custbody_createdby.value != null) invoiceRecord.customFieldList.StringCustomFieldRef__custbody_createdby.value else null,
	        "otherrefnum": if (invoiceRecord.otherRefNum != null) invoiceRecord.otherRefNum else null,
	        "emailSentToCustomer":if (invoiceRecord.customFieldList.BooleanCustomFieldRef__custbody_emailsenttocustomer.value != null) invoiceRecord.customFieldList.BooleanCustomFieldRef__custbody_emailsenttocustomer.value else null,
	        "memo": if (invoiceRecord.memo != null) invoiceRecord.memo else null,
		    
		            "invoiceLines": invoiceRecord.itemList.*item map (line) -> {
						"line": (if (line.line != null) line.line else null),
		                "amount": (if (line.amount != null) line.amount else null),
		                "rate": (if (line.rate != null) line.rate else null),
		                "item": (if (line.item.@internalId != null) (line.item.@internalId as Number) else null),
		                "quantity": (if (line.quantity != null) line.quantity else null),
		                "artemisInvoiceLineId": (if (line.customFieldList.StringCustomFieldRef__custcol_artemisinvoicelineid.value != null) line.customFieldList.StringCustomFieldRef__custcol_artemisinvoicelineid.value else null),
          				"invoiceItemNumb": (if (line.customFieldList.StringCustomFieldRef__custcol_invoiceitemnumb.value != null) line.customFieldList.StringCustomFieldRef__custcol_invoiceitemnumb.value else null),
          				"3dPositionId": (if (line.customFieldList.StringCustomFieldRef__custcol_3d_position_id.value != null) line.customFieldList.StringCustomFieldRef__custcol_3d_position_id.value else null),
					} default null
				}
			}
}
else {
    "code": 200,
    "status": "SUCCESS",
    "transactionId": vars.vTransactionId,
    "response": {
      "errorType": vars.vRetrieveInvoiceResponse.ERROR_TYPE,
      "details": vars.vRetrieveInvoiceResponse.ERROR_MSG
    }
}]]></ee:set-payload>
			</ee:message>
			<ee:variables >
			</ee:variables>
		</ee:transform>
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Response Payload"
			doc:id="47f9e2dc-4389-48de-8349-9bbf4f680263"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;  	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-get-netsuite-invoice",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/invoices",&#10;	"FinalResponse": payload,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response"
			doc:id="04e50be5-9a16-4332-9d79-80445185bd03"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;  	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-get-netsuite-invoice",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "/api/invoices",&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit"
			doc:id="e4e3ba6f-a648-4e38-8bd0-402f7bf7227e"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;  	"Message": "Flow Ended",&#10;	"FlowName": "pf-get-netsuite-invoice",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />

	</flow>
	<sub-flow name="sf-retrieve-netsuite-invoice" doc:id="de819fb4-35cb-4f71-9216-7a7d9a5625d3" >
		<ee:transform doc:name="Set vRetrieveInvoiceBody" doc:id="f35c3720-89b9-4580-902a-691abe288703">
			<ee:message>
			</ee:message>
			<ee:variables>
				<ee:set-variable variableName="vRetrieveInvoiceBody" ><![CDATA[%dw 2.0
output application/xml
ns ns0 urn:messages_2021_1.platform.webservices.netsuite.com
ns ns01 urn:common_2021_1.platform.webservices.netsuite.com
ns ns02 urn:core_2021_1.platform.webservices.netsuite.com
ns ns03 urn:relationships_2021_1.lists.webservices.netsuite.com
ns ns04 urn:sales.transactions.webservices.netsuite.com
ns xsi http://www.w3.org/2001/XMLSchema-instance
---
{
	ns0#get: {
		ns0#baseRef @("xmlns:ns0": ns0, "xmlns:ns02": ns02, xsi#"type": "ns02:RecordRef", "type": "invoice", ("internalId": (vars.vInternalId as Number)) if(!isEmpty(vars.vInternalId))): null
	}
}]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<netsuite:get doc:name="Retrieve Invoice data" doc:id="cedb358a-b798-40eb-813b-1530854b45d5" config-ref="NetSuite_Config" refType="RecordRef" type="invoice">
			<error-mapping sourceType="NETSUITE:NETSUITE_ERROR" targetType="CUSTOMNS:RECORD_DOES_NOT_EXIST" />
			<reconnect frequency="${netsuite.retry.timePeriod}" count="${netsuite.retry.maxRetries}" />
			<netsuite:message><![CDATA[#[vars.vRetrieveInvoiceBody]]]></netsuite:message>
		</netsuite:get>
		<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Raw NS Response " doc:id="0309e697-4f3e-41ce-9f5b-85611e03b313" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;  	"Message" : "Log Outbound Raw NS Resonse",&#10;	"FlowName" : "pf-get-netsuite-invoice",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/invoices",&#10;	"RawResponse": payload,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
	</sub-flow>
</mule>