<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:netsuite="http://www.mulesoft.org/schema/mule/netsuite"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/netsuite http://www.mulesoft.org/schema/mule/netsuite/current/mule-netsuite.xsd">
	<flow name="pf-update-netsuite-invoice"
		doc:id="5e872c2b-e452-4b63-9080-f2bf7f55b220">
		<logger level="INFO" doc:name="LOG INFO: Log Entry"
			doc:id="a76dba84-5490-47ce-9c20-cbe08720f035"
			message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-update-netsuite-invoice", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request"
			doc:id="1fb724b8-2aee-4777-bdda-0d676957811a"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;  	"Message" : "Log Outbound Request",&#10;	"FlowName" : "pf-update-netsuite-invoice",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/invoices",&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<ee:transform doc:name="Set vUpdateInvoiceBody"
			doc:id="6974b0d8-faa5-46be-87d2-b6eaee3d77de">
			<ee:message>
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="vUpdateInvoiceBody" ><![CDATA[%dw 2.0
output application/xml
ns ns0 urn:messages.platform.webservices.netsuite.com
ns ns01 urn:sales.transactions.webservices.netsuite.com
ns ns02 urn:core.platform.webservices.netsuite.com
ns ns03 urn:common.platform.webservices.netsuite.com 
ns xsi http://www.w3.org/2001/XMLSchema-instance
---
{
  ns0#update: {
    ns0#record @("xmlns:ns0": ns0,"xmlns:ns01": ns01, xsi#"type": "ns01:Invoice", ("internalId": payload.invoice.'invoiceNSId')): {
      (ns01#approvalStatus @(internalId: payload.invoice.'approvalStatus'): {}) if payload.invoice.'approvalStatus' != null,
      (ns01#tranDate : payload.invoice.'tranDate') if payload.invoice.'tranDate' != null,
      (ns01#customFieldList: {        
        (BooleanCustomFieldRef__custbodyresynccomplete: {
          ns02#value: payload.invoice.'resyncComplete'
        }) if payload.invoice.'resyncComplete' != null,
        (BooleanCustomFieldRef__custbody_resync: {
          ns02#value: payload.invoice.'resyncToSF'
        }) if payload.invoice.'resyncToSF' != null,
        (StringCustomFieldRef__custbody_commentsfromsf: {
          ns02#value: payload.invoice.'comments'
        }) if payload.invoice.'comments' != null,
        (StringCustomFieldRef__custbody_salesforce_id: {
          ns02#value: payload.invoice.'invoiceSFId'
        }) if payload.invoice.'invoiceSFId' != null,
        (StringCustomFieldRef__custbody_invoicesynctosf: {
          ns02#value: payload.invoice.'invoiceSyncToSF'
        }) if payload.invoice.'invoiceSyncToSF' != null,
        (SelectCustomFieldRef__custbody_statussync : {
          ns02#value @(internalId: payload.invoice.'statusSync'): {}
        }) if payload.invoice.'statusSync' != null,
        (StringCustomFieldRef__custbody_invoicesynctoartemis: {
          ns02#value: payload.invoice.'invoiceSyncToArtemis'
        }) if payload.invoice.'invoiceSyncToArtemis' != null,
        (SelectCustomFieldRef__custbody_statussynctoartemis : {
          ns02#value @(internalId: payload.invoice.'statusSyncToArtemis'): {}
        }) if payload.invoice.'statusSyncToArtemis' != null,
     	(DateCustomFieldRef__custbody_latestpaymentdate : {
          ns02#value: payload.invoice.lastPaymentDate
        }) if(payload.invoice.lastPaymentDate != null),
        (DoubleCustomFieldRef__custbody_clientamountpaid : {
          ns02#value: payload.invoice.clientAmountPaid
        }) if(payload.invoice.clientAmountPaid != null),
        (DoubleCustomFieldRef__custbody_3dcreditedamount : {
          ns02#value: payload.invoice.'3dCreditedAmount'
        }) if(payload.invoice.'3dCreditedAmount' != null),
        (StringCustomFieldRef__custbody_3d_confirm_id : {
          ns02#value: payload.invoice.'3dConfirmId'
        }) if(payload.invoice.'3dConfirmId' != null),
        (StringCustomFieldRef__custbody_artemisinvoiceid : {
          ns02#value: payload.invoice.'artemisInvoiceId'
        }) if(payload.invoice.'artemisInvoiceId' != null)
        
      }),
        (ns01#itemList: {
        	ns01#item: payload.invoice.'invoiceLines' map() -> {
        		(ns01#line: ($).'lineId') if(!isEmpty(($).'lineId')),
        		(ns01#customFieldList: {
        			(StringCustomFieldRef__custcol_artemisinvoicelineid: {
		              ns02#value: ($).artemisInvoiceLineId
		            })  if(!isEmpty(($).artemisInvoiceLineId)),
		            (StringCustomFieldRef__custcol_invoiceitemnumb: {
		              ns02#value: ($).invoiceItemNumb
		            })  if(!isEmpty(($).invoiceItemNumb)),
		            (StringCustomFieldRef__custcol_3d_position_id: {
		            	ns02#value: ($).'3dPositionId'
		            })  if(!isEmpty(($).'3dPositionId'))
		            	
		            
        		}
        			
        		)
        	}
        })if(!isEmpty(payload.invoice.'invoiceLines'))
    }
  }
}]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<try doc:name="Try" doc:id="03408d3a-a2b7-4a31-bfc8-daee2450941e">
			<set-variable value='#["0" as Number]'
				doc:name="vRetryCount" doc:id="f4cd8656-b2aa-41eb-97dd-9717c69b58d5"
				variableName="vRetryCount" />
			<until-successful
				maxRetries="${netsuite.retry.maxRetries}" doc:name="Until Successful"
				doc:id="062c2c91-b98f-4a35-b433-a437dbb78609"
				millisBetweenRetries="${netsuite.retry.timePeriod}">
				<try doc:name="Try"
					doc:id="92916a17-ca42-41ce-a5c2-ed57dbc8c89e">
					<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Request Payload" doc:id="d588a7d9-0944-47d4-a3ef-432053dbaef2" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;  	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "pf-update-netsuite-invoice",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/invoices",&#10;	"BackendRequest": vars.vUpdateInvoiceBody,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
					<netsuite:update doc:name="Update Invoice" doc:id="4fa61162-ec37-4c7b-aca8-c7a891d6ea34" config-ref="NetSuite_Config" type="invoice" target="vUpdateInvoiceResponse">
						<netsuite:message ><![CDATA[#[vars.vUpdateInvoiceBody]]]></netsuite:message>
					</netsuite:update>
					<logger level="DEBUG" doc:name="LOG DEBUG: After Invoice update" doc:id="3de67df8-ddab-4730-a48c-569bb9e709d0" message='#[%dw 2.0&#10;output application/json writeAttributes=true&#10;---&#10;{ 	&#10;	"Message" : "After Invoice update",&#10;	"FlowName" : "pf-update-netsuite-invoice",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/invoices",&#10;	"BackendRequest": vars.vUpdateInvoiceResponse,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
					<error-handler>
						<on-error-propagate enableNotifications="true"
							logException="true" doc:name="On Error Propagate"
							doc:id="1b32ef14-404b-4b7a-a6dc-166b7ac87f93"
							type="NETSUITE:CONNECTIVITY, NETSUITE:SESSION_TIMED_OUT">
							<set-variable
								value="#[(vars.vRetryCount as Number) + 1]"
								doc:name="Update vRetryCount"
								doc:id="d36c54ea-670c-4d8d-9be2-934baba78afd"
								variableName="vRetryCount" />
						</on-error-propagate>
						<on-error-continue enableNotifications="true"
							logException="true" doc:name="On Error Continue"
							doc:id="9641362b-19ca-4c4e-b60e-212cead3e996"
							type="NETSUITE:INVALID_VERSION, NETSUITE:NETSUITE_ERROR, NETSUITE:NETSUITE_SOAP_FAULT, NETSUITE:USER_ERROR">
							<ee:transform doc:name="set vUpdateInvoiceResponse" doc:id="9e323c8a-2e53-467d-a6d8-67939f2c2c7b" >
								<ee:message >
								</ee:message>
								<ee:variables >
									<ee:set-variable variableName="vUpdateInvoiceResponse" ><![CDATA[output application/json
---
{  
    "ERROR_MSG": error.description replace "\"" with "",
    "ERROR_TYPE": error.errorType.identifier
}]]></ee:set-variable>
								</ee:variables>
							</ee:transform>
						</on-error-continue>
					</error-handler>
				</try>
			</until-successful>
			<ee:transform doc:name="Set payload" doc:id="53ddeaaa-9ea6-4e88-8fe5-710671967548" >
				<ee:message >
					<ee:set-payload ><![CDATA[output application/json
var updateStatus = vars.vUpdateInvoiceResponse.updateResponse.writeResponse default {}
---
if(!isEmpty(updateStatus)) {
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "SUCCESS",
  "response": {
	"invoiceNSId": updateStatus.baseRef.@internalId default null,
	"success": true,
    "message": "updated successfully",
    "errorType": ""
  }
}
else  {
    "code": 400,
    "status": "FAILURE",
    "transactionId": vars.vTransactionId,
    "response": {
      "message": vars.vUpdateInvoiceResponse.ERROR_TYPE,
      "details": vars.vUpdateInvoiceResponse.ERROR_MSG
    }
}]]></ee:set-payload>
				</ee:message>
				<ee:variables >
					<ee:set-variable variableName="httpStatus" ><![CDATA[output application/json
var updateStatus = vars.vUpdateInvoiceResponse.updateResponse.writeResponse.status.@isSuccess ~= "true"
---
if(updateStatus default false) 200 else 400]]></ee:set-variable>
				</ee:variables>
			</ee:transform>
			<error-handler>
				<on-error-propagate enableNotifications="true"
					logException="true" doc:name="On Error Propagate"
					doc:id="e5e8690f-4ac6-4621-a08e-3eeb6beb09e9" type="ANY">
					<logger level="ERROR" doc:name="LOG ERROR: Exception occurred while updating Invoice" doc:id="061ed044-7da5-419c-9a18-e77a75ea41b1" message='#[output application/json&#10;---&#10;{ 	&#10;	"Message" : "Error occured while updating invoice",&#10;	"FlowName" : "pf-update-netsuite-invoice",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/invoices",&#10;	"ErrorDescription": error.description,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
				</on-error-propagate>
			</error-handler>
		</try>
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Response Payload"
			doc:id="eea7c2b8-aef2-48bb-8711-486ce5891032"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;  	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-update-netsuite-invoice",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/invoices",&#10;	"FinalResponse": payload,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response"
			doc:id="53f2adde-f311-4bcb-a5d4-224b2a836cd7"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;  	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-update-netsuite-invoice",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "/api/invoices",&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit"
			doc:id="7ab0ad2f-940f-4a92-b1b5-cecdd1a5e6fe"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;  	"Message": "Flow Ended",&#10;	"FlowName": "pf-update-netsuite-invoice",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
	</flow>
</mule>
