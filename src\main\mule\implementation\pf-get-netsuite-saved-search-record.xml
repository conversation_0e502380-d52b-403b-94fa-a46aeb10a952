<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns:netsuite="http://www.mulesoft.org/schema/mule/netsuite"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/netsuite http://www.mulesoft.org/schema/mule/netsuite/current/mule-netsuite.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd">
	<flow name="pf-get-netsuite-saved-search-record" doc:id="2d0e242a-e698-47e2-a860-c1230a4b4c40" >
		<logger level="INFO" doc:name="LOG INFO: Log Entry" doc:id="f8147f05-f9b4-41c8-acea-a09543cafd92" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-get-netsuite-saved-search-record", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Inbound Request" doc:id="32f738b8-b3b5-4353-87d0-692b8444e143" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Inbound Request",&#10;	"FlowName" : "pf-get-netsuite-saved-search-record",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/lookup/savedSearch",&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="DEBUG" doc:name="LOG DEBUG: Log Inbound Request Payload" doc:id="7c7960f9-f421-489a-b264-cbffea3f3890" message="#[%dw 2.0&#10;import * from dw::util::Values&#10;output application/json &#10;---&#10;{ 	&#10;	&quot;Message&quot; : &quot;Log Inbound Request Payload&quot;,&#10;	&quot;FlowName&quot; : &quot;pf-get-netsuite-saved-search-record&quot;,&#10;	&quot;CorrelationID&quot; : vars.vCorrelationId,&#10;	&quot;Endpoint&quot;: &quot;/api/lookup/savedSearch&quot;,&#10;	&quot;BackendRequest&quot;: payload,&#10;	&quot;BusinessKey&quot; : vars.vBusinessKey,&#10;	&quot;Attributes&quot;: attributes mask field('client_id') with &quot;*****&quot; mask field('client_secret') with &quot;*****&quot;&#10;}]" />
		<ee:transform doc:name="vKey, vId" doc:id="855c79fc-0181-42d0-8fa3-a254636157a6">
			<ee:message />
			<ee:variables>
				<ee:set-variable variableName="vKey" ><![CDATA[%dw 2.0
output application/json
---
attributes.queryParams.key]]></ee:set-variable>
				<ee:set-variable variableName="vId" ><![CDATA[%dw 2.0
output application/json
---
attributes.queryParams.id]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<ee:transform doc:name="Set vSearchRecordBody" doc:id="f5e1830b-c1b6-4bea-b4b5-6ad34449d301" >
			<ee:message >
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="vSearchRecordBody" ><![CDATA[%dw 2.0
output application/xml
ns ns0 urn:messages.platform.webservices.netsuite.com
ns ns01 urn:accounting.lists.webservices.netsuite.com
ns ns02 urn:accounting.lists.webservices.netsuite.com
---
{
	ns0#search: {
		ns0#searchRecord @(("xmlns:ns01": ns01),
      	xsi#'type': "ns01:" ++ Mule::p(vars.vKey), savedSearchId: vars.vId): {}
	}
}]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<netsuite:search doc:name="Fetch record" doc:id="a0c3f37c-4ca5-4018-85dd-13c010849933" config-ref="NetSuite_Config" key="#[Mule::p(vars.vKey)]" target="vRetrieveRecordResponse">
			<netsuite:message ><![CDATA[#[vars.vSearchRecordBody]]]></netsuite:message>
		</netsuite:search>
		<ee:transform doc:name="Set payload, httpStatus" doc:id="bddaa2ff-fcf8-4a72-a8e8-40adb7fa21c4">
					<ee:message>
						<ee:set-payload><![CDATA[%dw 2.0
output application/json writeAttributes = true
---
if(!isEmpty(vars.vRetrieveRecordResponse)) {
"code": 200,
    "status": "SUCCESS",
    "transactionId": vars.vTransactionId,
	"response": {
		(vars.vKey as String): (
			vars.vRetrieveRecordResponse map() -> {
			    "internalId": ($).payload.searchRow.basic.internalId.searchValue.@internalId,
			    "searchValue": ($).payload.searchRow.basic.name.searchValue
			}
		)
	}
}
else {
    "code": 200,
    "status": "SUCCESS",
    "transactionId": vars.vTransactionId,
    "response": {
      "message": "RECORD_NOT_FOUND",
      "details": "Key " ++ (vars.vKey default "") ++ ", SavedSearchId " ++ (vars.vId default "") ++ " is invalid combination" 
    }
}]]></ee:set-payload>
					</ee:message>
			<ee:variables >
				<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/json
---
200]]></ee:set-variable>
			</ee:variables>
				</ee:transform>
		<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Response Payload" doc:id="e34f111a-9cc7-4c4b-be8f-42f5d58fba6e" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-get-netsuite-saved-search-record",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/lookup/savedSearch",&#10;	"BackendResponse": payload,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response" doc:id="9c0754d4-ffeb-40fa-950a-8f92b6ed4f27" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-get-netsuite-saved-search-record",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "/api/lookup/savedSearch",&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit" doc:id="cea36e03-3532-4da0-9b7d-e675b9caca5d" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-get-netsuite-saved-search-record",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
	</flow>
</mule>
