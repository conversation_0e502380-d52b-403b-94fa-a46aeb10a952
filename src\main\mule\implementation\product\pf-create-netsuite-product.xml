<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:netsuite="http://www.mulesoft.org/schema/mule/netsuite"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/netsuite http://www.mulesoft.org/schema/mule/netsuite/current/mule-netsuite.xsd">
	<flow name="pf-create-netsuite-product"
		doc:id="d395d98c-8d7c-4786-8fb3-0207d396193d">
		<logger level="INFO" doc:name="LOG INFO: Log Entry"
			doc:id="a441044b-1a5c-4113-b572-5fe52cf5bea7"
			message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-create-netsuite-product", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request"
			doc:id="07135926-787c-470a-904f-bbdabc31c320"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "pf-create-netsuite-product",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/product"&#10;}]' />
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Request Payload"
			doc:id="43e5c286-6e51-4ca4-821d-af9355671300"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "pf-create-netsuite-product",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/product",&#10;	"BackendRequest": payload&#10;}]' />
		<ee:transform doc:name="Set vDbRecord, vRequestAttributes" doc:id="9248301c-5cd4-4fdd-aa04-cbbf6ed4e347" >
			<ee:message >
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="vDbRecord" ><![CDATA[%dw 2.0
import * from dw::core::Strings
import * from dw::core::Arrays
output application/json
---
{
	"CORRELATION_ID": vars.vCorrelationId,
	"OBJECT_TYPE": "PRODUCT",
	"OPERATION": "CREATE",
	"SOURCE": vars.vAttributes.headers.'x-source' default null,
	"TASK_PAYLOAD": write(payload, "application/json") default null,
	"DESTINATION": "NETSUITE",
	"STATUS": "IN_PROGRESS",
	"RETRY_COUNT": 0,
	"LAST_UPDATED_BY": "SYSTEM_API",
	"QUERY_PARAMS": ((((vars.vAttributes.queryParams mapObject() -> {
	    "key": ($$),
	    "val": ($)
	}) default {} pluck($) divideBy(2)) map() -> (($) joinBy "|")) joinBy "||") default null,
	"ERROR_MSG": null,
    "ERROR_TYPE": null
}]]></ee:set-variable>
				<ee:set-variable variableName="vRequestAttributes" ><![CDATA[%dw 2.0
output application/json
---
{
	"headers": {
		"x-source": vars.vAttributes.headers.'x-source' default "",
		"x-transactionId": vars.vTransactionId,
		"x-msg-timestamp": now() as DateTime,
		"correlationId": vars.vCorrelationId,
		"sourceId": "NETSUITE_SYS_API",
		"destinationId": "TRANSACTION_DB_SYS_API",
		"content-type": "application/json"
	}
}]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<choice doc:name="Check header context" doc:id="b60ecde2-e011-44b7-b0ad-287dcfa259a1" >
			<when expression="#[(vars.vAttributes.headers.'context' ~= &quot;REGULAR&quot;)]">
				<flow-ref doc:name="Flow Reference to sf-insert-into-transaction-task-details-table" doc:id="04074a25-0d61-41eb-b84a-a516c2b64f28" name="sf-insert-into-transaction-task-details-table" />
			</when>
			<when expression="#[((vars.vAttributes.headers.'context' ~= &quot;ROLLBACK&quot;) or (vars.vAttributes.headers.'context' ~= &quot;RETRY&quot;))]">
				<logger level="INFO" doc:name="LOG INFO: Skip STATUS update" doc:id="493b7744-b208-4351-b0b9-77acdafdcc1f" message="#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	&quot;Message&quot; : &quot;No update is made to TRANSACTION_TASK_DETAILS table as headers context is &quot; ++ (vars.vAttributes.headers.'context' default &quot;&quot;),&#10;	&quot;FlowName&quot; : &quot;pf-create-netsuite-product&quot;,&#10;	&quot;CorrelationID&quot; : vars.vCorrelationId&#10;}]" />
			</when>
			<otherwise >
				<raise-error doc:name="CUSTOM:INVALID_HEADER_CONTEXT" doc:id="961501bb-59a1-4c8b-89aa-4be069fc3315" type="CUSTOM:INVALID_HEADER_CONTEXT" description="#[(&quot;Invalid value for context &quot; ++ (vars.vAttributes.headers.'context' default &quot;null&quot;) ++ &quot; is passed&quot;)]"/>
			</otherwise>
		</choice>
		<ee:transform doc:name="Set vCreateProductBody"
			doc:id="174ce940-1bd5-431c-82d5-0b2a2e2cb6c9">
			<ee:message>
			</ee:message>
			<ee:variables >
				<ee:set-variable resource="dwl/create/createNetsuiteProductMapping.dwl" variableName="vCreateProductBody" />
				<ee:set-variable variableName="vProductType" ><![CDATA[payload.product.productType]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<try doc:name="Try" doc:id="b84bf4c8-de92-4d97-8994-5caf8093d07f">
			<set-variable value='#["0" as Number]'
				doc:name="vRetryCount" doc:id="6d7a5f14-2d23-4959-a918-b97dd06b6cfe"
				variableName="vRetryCount" />
			<until-successful
				maxRetries="${netsuite.retry.maxRetries}" doc:name="Until Successful"
				doc:id="4c83c90a-a241-4435-9ecf-eb28c61077e7"
				millisBetweenRetries="${netsuite.retry.timePeriod}">
				<try doc:name="Try"
					doc:id="6d8c82a6-6620-459f-b70f-ed99f473a13f">
					<netsuite:add doc:name="Add Items" doc:id="9c7f727f-8f99-4667-988f-e4670c515125" config-ref="NetSuite_Config" type="#[vars.vProductType]" target="vAddProductResponse">
				<netsuite:message><![CDATA[#[vars.vCreateProductBody]]]></netsuite:message>
			</netsuite:add>
					<ee:transform doc:name="Update vDbRecord, vRequestAttributes" doc:id="06785fda-805c-41c6-bc47-99e249d23dc7" >
						<ee:message >
						</ee:message>
						<ee:variables >
							<ee:set-variable variableName="vDbRecord" ><![CDATA[%dw 2.0
import * from dw::core::Strings
output application/json
---
vars.vDbRecord update {
	case STATUS at .STATUS -> "COMPLETED"
    case RETRY_COUNT at .RETRY_COUNT -> vars.vRetryCount as Number   
}]]></ee:set-variable>
							<ee:set-variable variableName="vRequestAttributes" ><![CDATA[%dw 2.0
output application/json
---
vars.vRequestAttributes update {
	case timestamp at .headers.'x-msg-timestamp' -> now()
}]]></ee:set-variable>
						</ee:variables>
					</ee:transform>
					<error-handler>
						<on-error-propagate enableNotifications="true"
							logException="true" doc:name="On Error Propagate"
							doc:id="a2c0a26c-fa39-4257-9512-03dddda4ac42"
							type="NETSUITE:CONNECTIVITY, NETSUITE:SESSION_TIMED_OUT">
							<set-variable
								value="#[(vars.vRetryCount as Number) + 1]"
								doc:name="Update vRetryCount"
								doc:id="9b79ed9d-1404-4bae-b88d-b6ea4142a864"
								variableName="vRetryCount" />
						</on-error-propagate>
						<on-error-continue enableNotifications="true"
							logException="true" doc:name="On Error Continue"
							doc:id="59652480-4868-4667-97a5-a2a88b18fdbe"
							type="NETSUITE:INVALID_VERSION, NETSUITE:NETSUITE_ERROR, NETSUITE:NETSUITE_SOAP_FAULT, NETSUITE:USER_ERROR">
							<ee:transform doc:name="Update vDbRecord, vRequestAttributes" doc:id="bbdf23ec-8044-4ffa-83df-0f5358c62501" >
								<ee:message >
								</ee:message>
								<ee:variables >
									<ee:set-variable variableName="vDbRecord" ><![CDATA[output application/json
---
vars.vDbRecord update {
	case STATUS at .STATUS ->  "FAILED"
    case RETRY_COUNT at .RETRY_COUNT -> vars.vRetryCount as Number   
    case ERROR_MSG at .ERROR_MSG -> error.description replace "\"" with ""
    case ERROR_TYPE at .ERROR_TYPE -> "DATA"
}]]></ee:set-variable>
									<ee:set-variable variableName="vRequestAttributes" ><![CDATA[%dw 2.0
output application/json
---
vars.vRequestAttributes update {
	case timestamp at .headers.'x-msg-timestamp' -> now()
}]]></ee:set-variable>
								</ee:variables>
							</ee:transform>
						</on-error-continue>
					</error-handler>
				</try>
			</until-successful>
			<flow-ref
				doc:name="Flow Reference to sf-update-transaction-task-details-table"
				doc:id="727a5177-56f1-4cff-97c2-1a42db641c78"
				name="sf-update-transaction-task-details-table" />
			<ee:transform doc:name="Set payload, httpStatus" doc:id="d581db6c-bce9-4555-a0b4-a6296e132a20" >
				<ee:message >
					<ee:set-payload ><![CDATA[output application/json
var createStatus = ((vars.vDbRecord.'STATUS' ~= "COMPLETED") and (vars.vAddProductResponse.addResponse.writeResponse.status.@isSuccess ~= "true"))
---
if(createStatus) {
  "code": 201,
  "transactionId": vars.vTransactionId,
  "status": "SUCCESS",
  "response": {
    "internalid": vars.vAddProductResponse.addResponse.writeResponse.baseRef.@internalId default "",
    "eid": payload.product.'eid' default ""
  }
}
else  {
    "code": 400,
    "status": "FAILURE",
    "transactionId": vars.vTransactionId,
    "response": {
      "message": vars.vDbRecord.'ERROR_TYPE',
      "details": vars.vDbRecord.'ERROR_MSG'
    }
}]]></ee:set-payload>
				</ee:message>
				<ee:variables >
					<ee:set-variable variableName="httpStatus" ><![CDATA[output application/json
var createStatus = vars.vAddProductResponse.addResponse.writeResponse.status.@isSuccess ~= "true"
---
if(createStatus default false) 201 else 400  ]]></ee:set-variable>
				</ee:variables>
			</ee:transform>
			<error-handler>
				<on-error-propagate enableNotifications="true"
					logException="true" doc:name="On Error Propagate"
					doc:id="002988eb-5812-44f1-a537-8f2008ebec53" type="ANY">
					<ee:transform doc:name="Update vDbRecord, vRequestAttributes" doc:id="0e05870b-2d75-4702-ac3b-fad33cc741b7" >
						<ee:message >
						</ee:message>
						<ee:variables >
							<ee:set-variable variableName="vDbRecord" ><![CDATA[output application/json
---
vars.vDbRecord update {
    case STATUS at .STATUS ->  "FAILED"
    case RETRY_COUNT at .RETRY_COUNT -> p('netsuite.retry.maxRetries') as Number
    case ERROR_MSG at .ERROR_MSG -> error.description replace "\"" with ""
    case ERROR_TYPE at .ERROR_TYPE -> "SYSTEM"
}]]></ee:set-variable>
							<ee:set-variable variableName="vRequestAttributes" ><![CDATA[%dw 2.0
output application/json
---
vars.vRequestAttributes update {
	case timestamp at .headers.'x-msg-timestamp' -> now()
}]]></ee:set-variable>
						</ee:variables>
					</ee:transform>
					<flow-ref
						doc:name="Flow Reference to sf-update-transaction-task-details-table"
						doc:id="54651524-ab99-482c-b1e1-3e1f88e74bef"
						name="sf-update-transaction-task-details-table" />
				</on-error-propagate>
			</error-handler>
		</try>
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Response Payload"
			doc:id="03b49d44-a2ef-4c09-bc19-fbf499abd6c7"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-create-netsuite-product",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/product",&#10;	"BackendResponse": payload&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response"
			doc:id="d91831e8-902d-4449-b701-328361c22ee7"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-create-netsuite-product",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "/api/product"&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit"
			doc:id="e2dfa70c-3895-433d-87b8-bdb0fa6ac655"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-create-netsuite-product",&#10;	"CorrelationID": vars.vCorrelationId&#10;}]' />
	</flow>
</mule>
