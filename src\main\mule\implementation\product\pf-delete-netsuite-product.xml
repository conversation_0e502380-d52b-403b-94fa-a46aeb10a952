<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns:netsuite="http://www.mulesoft.org/schema/mule/netsuite"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/netsuite http://www.mulesoft.org/schema/mule/netsuite/current/mule-netsuite.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd">
	<flow name="pf-delete-netsuite-product" doc:id="c98921c3-cd55-46b6-994e-914fe1285890" >
		<logger level="INFO" doc:name="LOG INFO: Log Entry" doc:id="370ec383-bb79-4e23-a0f1-409d95a52d28" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-delete-netsuite-product", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request" doc:id="6c3607c8-0081-46b8-a092-fd8dabe70a32" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "pf-delete-netsuite-product",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/product"&#10;}]' />
		<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Request Payload" doc:id="8ac272b4-80fd-42f6-a088-a8b22eb891fa" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "pf-delete-netsuite-product",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/product",&#10;	"BackendRequest": payload&#10;}]' />
		
		<ee:transform doc:name="productType and camelizeProductType" doc:id="3a5d5965-6441-4978-9599-c823864793fe" >
			<ee:message >
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="vProductType" ><![CDATA[attributes.queryParams.productType]]></ee:set-variable>
				<ee:set-variable variableName="vCamelizeProductType" ><![CDATA[%dw 2.0
import * from dw::core::Strings
output application/json
---

camelize(attributes.queryParams.productType)]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<ee:transform doc:name="Set vDbRecord, vRequestAttributes" doc:id="97994814-15de-462f-bc1f-03ed973bc7b1" >
			<ee:message >
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="vDbRecord" ><![CDATA[%dw 2.0
import * from dw::core::Strings
import * from dw::core::Arrays
output application/json
---
{
	"CORRELATION_ID": vars.vCorrelationId,
	"OBJECT_TYPE": "PRODUCT",
	"OPERATION": "DELETE",
	"SOURCE": vars.vAttributes.headers.'x-source' default null,
	"TASK_PAYLOAD": write(payload, "application/json") default null,
	"DESTINATION": "NETSUITE",
	"STATUS": "IN_PROGRESS",
	"RETRY_COUNT": 0,
	"LAST_UPDATED_BY": "SYSTEM_API",
	"QUERY_PARAMS": ((((vars.vAttributes.queryParams mapObject() -> {
	    "key": ($$),
	    "val": ($)
	}) default {} pluck($) divideBy(2)) map() -> (($) joinBy "|")) joinBy "||") default null,
	"ERROR_MSG": null,
    "ERROR_TYPE": null
}]]></ee:set-variable>
				<ee:set-variable variableName="vRequestAttributes" ><![CDATA[%dw 2.0
output application/json
---
{
	"headers": {
		"x-source": vars.vAttributes.headers.'x-source' default "",
		"x-transactionId": vars.vTransactionId,
		"x-msg-timestamp": now() as DateTime,
		"correlationId": vars.vCorrelationId,
		"sourceId": "NETSUITE_SYS_API",
		"destinationId": "TRANSACTION_DB_SYS_API",
		"content-type": "application/json"
	}
}]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		
		<ee:transform doc:name="Set vInternalId, vEnterpriseId"
			doc:id="6f26a878-631c-4e82-8b28-af58901c425b">
			<ee:message />
			<ee:variables>
				<ee:set-variable variableName="vInternalId"><![CDATA[%dw 2.0
output application/json
---
attributes.queryParams.'internalId' default ""]]></ee:set-variable>
				<ee:set-variable variableName="vEnterpriseId" ><![CDATA[%dw 2.0
output application/json
---
attributes.queryParams.'eid' default ""]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<choice doc:name="Check header context" doc:id="91a8a305-332a-43e6-989d-fade188ab1d2">
			<when expression="#[(vars.vAttributes.headers.'context' ~= &quot;REGULAR&quot;)]">
				<flow-ref doc:name="Flow Reference to sf-insert-into-transaction-task-details-table" doc:id="c069c51f-e236-4c7b-98ff-23facfa8959c" name="sf-insert-into-transaction-task-details-table" />
			</when>
			<when expression="#[((vars.vAttributes.headers.'context' ~= &quot;ROLLBACK&quot;) or (vars.vAttributes.headers.'context' ~= &quot;RETRY&quot;))]">
				<logger level="INFO" doc:name="LOG INFO: Skip STATUS update" doc:id="79f78113-c477-4075-bca0-401310f83f2d" message="#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	&quot;Message&quot; : &quot;No update is made to TRANSACTION_TASK_DETAILS table as headers context is &quot; ++ (vars.vAttributes.headers.'context' default &quot;&quot;),&#10;	&quot;FlowName&quot; : &quot;pf-delete-netsuite-product&quot;,&#10;	&quot;CorrelationID&quot; : vars.vCorrelationId&#10;}]" />
			
</when>
			<otherwise>
				<raise-error doc:name="CUSTOM:INVALID_HEADER_CONTEXT" doc:id="d8cb0ba9-011f-488d-a23b-642f613145c1" type="CUSTOM:INVALID_HEADER_CONTEXT" description="#[(&quot;Invalid value for context &quot; ++ (vars.vAttributes.headers.'context' default &quot;null&quot;) ++ &quot; is passed&quot;)]" />
			</otherwise>
		</choice>
		<choice doc:name="Check if vInternalId is empty"
			doc:id="1bbb5350-496a-44b3-a733-c9f09a14750d">
			<when expression="#[isEmpty(vars.vInternalId)]">
				<flow-ref
					doc:name="Flow Reference to sf-get-netsuite-internal-id"
					doc:id="1c83b294-486c-4508-abb8-4c9360088545"
					name="sf-get-netsuite-internal-id" target="vInternalId" />
			</when>
			<otherwise >
				<flow-ref doc:name="Flow Reference to sf-get-netsuite-enterprise-id" doc:id="3c25acfd-3fde-40ae-9c3a-2f87cc8cb22b" name="sf-get-netsuite-enterprise-id" target="vEnterpriseId" />
			</otherwise>
		</choice>
		
		<ee:transform doc:name="Set vDeleteProductBody" doc:id="fb0cc3f9-101f-4688-b869-8ecd91308b18" >
			<ee:message >
			</ee:message>
			<ee:variables >
				<ee:set-variable resource="dwl/delete/deleteNetsuiteProductMapping.dwl" variableName="vDeleteProductBody" />
			</ee:variables>
		</ee:transform>
		<try doc:name="Try" doc:id="869c7ec9-ed5a-43cb-a268-2f29c226dc4b">
			<set-variable value='#["0" as Number]' doc:name="vRetryCount" doc:id="705cdf50-ca1d-4eb4-96f9-1fe6fe2d2a88" variableName="vRetryCount" />
			<until-successful maxRetries="${netsuite.retry.maxRetries}" doc:name="Until Successful" doc:id="e8e999d0-cf26-4079-9b92-0b91f22b2ad9" millisBetweenRetries="${netsuite.retry.timePeriod}">
				<try doc:name="Try" doc:id="9d6ffe91-5199-46d8-ac64-45970566fbab">
					<netsuite:delete doc:name="Delete Product record" doc:id="5d0c3ef8-64eb-4663-b054-4c1117421075" config-ref="NetSuite_Config" refType="RecordRef" type="#[vars.vCamelizeProductType]" target="vDeleteProductResponse">
			<reconnect frequency="${netsuite.retry.timePeriod}" count="${netsuite.retry.maxRetries}" />
			<netsuite:message><![CDATA[#[vars.vDeleteProductBody]]]></netsuite:message>
		</netsuite:delete>
					<ee:transform doc:name="Update vDbRecord, vRequestAttributes" doc:id="93062749-ff3c-480e-8bd1-2b01bdb721c8">
						<ee:message />
						<ee:variables>
							<ee:set-variable variableName="vDbRecord"><![CDATA[%dw 2.0
import * from dw::core::Strings
output application/json
---
vars.vDbRecord update {
	case STATUS at .STATUS -> "COMPLETED"
    case RETRY_COUNT at .RETRY_COUNT -> vars.vRetryCount as Number   
}]]></ee:set-variable>
							<ee:set-variable variableName="vRequestAttributes"><![CDATA[%dw 2.0
output application/json
---
vars.vRequestAttributes update {
	case timestamp at .headers.'x-msg-timestamp' -> now()
}]]></ee:set-variable>
						</ee:variables>
					</ee:transform>
					<error-handler>
						<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="1b683adf-9bbc-4189-a421-735a769424aa" type="NETSUITE:CONNECTIVITY, NETSUITE:SESSION_TIMED_OUT">
							<set-variable value="#[(vars.vRetryCount as Number) + 1]" doc:name="Update vRetryCount" doc:id="b61993db-3db7-40a6-9036-c35d21f06522" variableName="vRetryCount" />
						</on-error-propagate>
						<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="0e2d9918-b92b-4ab9-924e-b56f3bd310fd" type="NETSUITE:INVALID_VERSION, NETSUITE:NETSUITE_ERROR, NETSUITE:NETSUITE_SOAP_FAULT, NETSUITE:USER_ERROR">
							<ee:transform doc:name="Update vDbRecord, vRequestAttributes" doc:id="b08c5a20-7161-4f75-8da8-f1be69f59674">
								<ee:message />
								<ee:variables>
									<ee:set-variable variableName="vDbRecord"><![CDATA[output application/json
---
vars.vDbRecord update {
	case STATUS at .STATUS -> "FAILED"
    case RETRY_COUNT at .RETRY_COUNT -> vars.vRetryCount as Number   
    case ERROR_MSG at .ERROR_MSG -> error.description replace "\"" with ""
    case ERROR_TYPE at .ERROR_TYPE -> "DATA"
}]]></ee:set-variable>
									<ee:set-variable variableName="vRequestAttributes"><![CDATA[%dw 2.0
output application/json
---
vars.vRequestAttributes update {
	case timestamp at .headers.'x-msg-timestamp' -> now()
}]]></ee:set-variable>
								</ee:variables>
							</ee:transform>
						</on-error-continue>
					</error-handler>
				</try>
			</until-successful>
			<flow-ref doc:name="Flow Reference to sf-update-transaction-task-details-table" doc:id="3ff3d4fb-b6aa-41ec-bfad-d91c96a59cbd" name="sf-update-transaction-task-details-table" />
			<ee:transform doc:name="Set payload, httpStatus" doc:id="c8b3c442-6e62-4a19-8557-41d933f6a84e">
			<ee:message>
				<ee:set-payload><![CDATA[%dw 2.0
output application/json
---
if(vars.vDbRecord.'STATUS' ~= "COMPLETED") {
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "SUCCESS",
  "response": {
    "internalid": vars.vInternalId,
    "eid": vars.vEnterpriseId,
    "message": "DELETED"
  }
}
else {
  "code": 400,
  "transactionId": vars.vTransactionId,
  "status": "FAILURE",
  "response": {
      "message": vars.vDbRecord.'ERROR_TYPE',
      "details": vars.vDbRecord.'ERROR_MSG'
  }
}
]]></ee:set-payload>
			</ee:message>
			<ee:variables>
				<ee:set-variable variableName="httpStatus"><![CDATA[output application/json
---
if(vars.vDbRecord.'STATUS' ~= "COMPLETED") 200 else 400]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
			<error-handler>
				<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="ac20ec48-51c9-49ac-b52c-8b6eb2af7a12" type="ANY">
					<ee:transform doc:name="Update vDbRecord, vRequestAttributes" doc:id="a5ed2f95-ad88-48e9-92cc-0fbb4d8e840c">
						<ee:message />
						<ee:variables>
							<ee:set-variable variableName="vDbRecord"><![CDATA[output application/json
---
vars.vDbRecord update {
    case STATUS at .status -> "FAILED"
    case RETRY_COUNT at .retryCount -> p('netsuite.retry.maxRetries') as String
    case ERROR_MSG at .ERROR_MSG -> error.description replace "\"" with ""
    case ERROR_TYPE at .ERROR_TYPE -> "SYSTEM"
}]]></ee:set-variable>
							<ee:set-variable variableName="vRequestAttributes"><![CDATA[%dw 2.0
output application/json
---
vars.vRequestAttributes update {
	case timestamp at .headers.'x-msg-timestamp' -> now()
}]]></ee:set-variable>
						</ee:variables>
					</ee:transform>
					<flow-ref doc:name="Flow Reference to sf-update-transaction-task-details-table" doc:id="2430d9bf-1ae6-4634-930a-fc1b0ce0f3f7" name="sf-update-transaction-task-details-table" />
				</on-error-propagate>
			</error-handler>
		</try>
		<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Response Payload" doc:id="23e57c2b-64cd-42f1-b2c0-0c0e64e6c040" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-delete-netsuite-product",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/product",&#10;	"BackendResponse": payload&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response" doc:id="b7c27b0b-fd09-4b32-bcb7-54536559c393" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-delete-netsuite-product",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "/api/product"&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit" doc:id="e7f7f4e9-f576-48ec-8022-2720250c0232" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-delete-netsuite-product",&#10;	"CorrelationID": vars.vCorrelationId&#10;}]' />
	</flow>
</mule>
