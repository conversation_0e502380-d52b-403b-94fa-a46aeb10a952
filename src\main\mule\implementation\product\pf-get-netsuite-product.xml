<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:netsuite="http://www.mulesoft.org/schema/mule/netsuite"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/netsuite http://www.mulesoft.org/schema/mule/netsuite/current/mule-netsuite.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd">
	<flow name="pf-get-netsuite-product"
		doc:id="2f2f005a-a798-4577-9ee9-c68e00d92fe5">
		<logger level="INFO" doc:name="LOG INFO: Log Entry"
			doc:id="28d3a3b8-08f4-400e-a839-f8b722454ec6"
			message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-get-netsuite-product", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request"
			doc:id="bd21621c-19fb-4638-806b-fa9e46bdb239"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "pf-get-netsuite-product",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/product"&#10;}]' />
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Request Payload"
			doc:id="0389b40d-9f48-406e-9791-f1588d314c0f"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "pf-get-netsuite-product",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/product",&#10;	"BackendRequest": payload&#10;}]' />
		<ee:transform doc:name="productType and camelizeProductType" doc:id="2e178ef1-4643-4179-b35e-2c31f2e5828f" >
			<ee:message >
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="vProductType" ><![CDATA[attributes.queryParams.productType]]></ee:set-variable>
				<ee:set-variable variableName="vCamelizeProductType" ><![CDATA[%dw 2.0
import * from dw::core::Strings
output application/json
---

camelize(attributes.queryParams.productType)]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<ee:transform doc:name="Set vInternalId, vEnterpriseId, vRequestAttributes"
			doc:id="1d94fb3e-994b-4241-9864-6b1c8b5154ab">
			<ee:message />
			<ee:variables>
				<ee:set-variable variableName="vInternalId"><![CDATA[%dw 2.0
output application/json
---
attributes.queryParams.'internalId' default ""]]></ee:set-variable>
				<ee:set-variable variableName="vEnterpriseId" ><![CDATA[%dw 2.0
output application/json
---
attributes.queryParams.'eid' default ""]]></ee:set-variable>
				<ee:set-variable variableName="vRequestAttributes" ><![CDATA[%dw 2.0
output application/json
---
{
	"headers": {
		"x-source": vars.vAttributes.headers.'x-source' default "",
		"x-transactionId": vars.vTransactionId,
		"x-msg-timestamp": now() as DateTime,
		"correlationId": vars.vCorrelationId,
		"sourceId": "NETSUITE_SYS_API",
		"destinationId": "TRANSACTION_DB_SYS_API",
		"content-type": "application/json"
	}
}]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<choice doc:name="Check if vInternalId is empty"
			doc:id="3e6c8c0d-d4da-404e-b869-6ef160c308ee">
			<when expression="#[isEmpty(vars.vInternalId)]">
				<flow-ref doc:name="sf-get-netsuite-internal-id" doc:id="6b4b20ee-8581-4233-beba-a63f269f91ad" name="sf-get-netsuite-internal-id"/>
			</when>
			<otherwise >
				<flow-ref doc:name="Flow Reference to sf-get-netsuite-enterprise-id" doc:id="a0a1231c-2f23-444e-a7c4-a4f6836113bb" name="sf-get-netsuite-enterprise-id" target="vEnterpriseId" />
			</otherwise>
		</choice>
		<try doc:name="Try" doc:id="d3afff1a-ee3e-469b-ad8e-85d78d87f377" >
			<flow-ref doc:name="Flow Reference to sf-retrieve-netsuite-product" doc:id="2b66ea23-1d0e-4b36-9c47-028cb4b0f989" name="sf-retrieve-netsuite-product" target="vRetrieveProductResponse" />
			<error-handler >
				<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="6ed394e4-e62e-4761-809b-d023c6edaf77" type="CUSTOMNS:RECORD_DOES_NOT_EXIST">
					<set-variable value="#[output application/json&#10;---&#10;{&#10;	&quot;getResponse&quot;: {&#10;		&quot;status&quot;: &quot;FAILURE&quot;,&#10;		&quot;detail&quot;: &quot;Record with internalid &quot; ++ vars.vInternalId ++ &quot; doesn't exist in Netsuite&quot;&#10;	}&#10;}]" doc:name="vRetrieveProductResponse" doc:id="f6beeeb6-d5b6-4770-990b-22159f9f1981" variableName="vRetrieveProductResponse"/>
				</on-error-continue>
			</error-handler>
		</try>
		<ee:transform doc:name="Set payload, httpStatus" doc:id="9472705d-b06d-4dcc-91b7-6e535ade1f7b" >
			<ee:message >
				<ee:set-payload ><![CDATA[%dw 2.0
import substringBeforeLast from dw::core::Strings
var productRecord = vars.vRetrieveProductResponse.getResponse.readResponse.record default {}
var modifiedByEmailAddress = productRecord.customFieldList.StringCustomFieldRef__custentity_last_modified_by.value
fun getUsernameFromEmailAddress(email) = (
	substringBeforeLast((email default ""), "@")
)
output application/json
---
if(!isEmpty(productRecord)) {
	"code": 200,
    "status": "SUCCESS",
    "transactionId": vars.vTransactionId,
	"response": {
		"product": {
		    "productupdatedby": (
		    	if((isEmpty(modifiedByEmailAddress)) or (lower(modifiedByEmailAddress) ~= Mule::p('netsuite.user.mulesoft.emailAddress')))
		    		productRecord.customFieldList.StringCustomFieldRef__custitem_item_updated_by.value default null
		    	else
		    		("NS" ++ "\\" ++ (productRecord.customFieldList.StringCustomFieldRef__custentity_last_modified_by.'value' default "")) default null
		    ),
		    "taxschedule": productRecord.taxSchedule.name default null,
		    "displayname": productRecord.displayName default null,
		    "salesDescription": productRecord.salesDescription default null,
		    "internalid": vars.vInternalId default null,
			//"candelete": productRecord.customFieldList.BooleanCustomFieldRef__custentity_candelete.value default null,
		    "candelete": "true",
		    "eid": vars.vEnterpriseId default null,
		    "custitem_syncmessage": productRecord.customFieldList.StringCustomFieldRef__custitem_syncmessage.'value' default "",
		    "custitem_itemsyncstatus": productRecord.customFieldList.SelectCustomFieldRef__custitem_itemsyncstatus.'value' default ""

		}
	}
}
else {
    "code": 200,
    "status": "SUCCESS",
    "transactionId": vars.vTransactionId,
    "response": {
      "message": "RECORD_NOT_FOUND",
      "details": vars.vRetrieveCustomerResponse.getResponse.detail
    }
}]]></ee:set-payload>
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="httpStatus" ><![CDATA[200]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Response Payload"
			doc:id="47f9e2dc-4389-48de-8349-9bbf4f680263"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-get-netsuite-product",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/product",&#10;	"BackendResponse": payload&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response"
			doc:id="04e50be5-9a16-4332-9d79-80445185bd03"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-get-netsuite-product",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "/api/product"&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit"
			doc:id="e4e3ba6f-a648-4e38-8bd0-402f7bf7227e"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-get-netsuite-product",&#10;	"CorrelationID": vars.vCorrelationId&#10;}]' />

	</flow>
	<sub-flow name="sf-retrieve-netsuite-product" doc:id="de819fb4-35cb-4f71-9216-7a7d9a5625d3" >
		<ee:transform doc:name="Set vRetrieveProductBody" doc:id="f35c3720-89b9-4580-902a-691abe288703">
			<ee:message>
			</ee:message>
			<ee:variables>
				<ee:set-variable resource="dwl/read/readNetsuiteProductMapping.dwl" variableName="vRetrieveProductBody" />
			</ee:variables>
		</ee:transform>
		<netsuite:get doc:name="RetriveServiceSaleItem" doc:id="cedb358a-b798-40eb-813b-1530854b45d5" config-ref="NetSuite_Config" refType="RecordRef" type="#[vars.vCamelizeProductType]">
			<error-mapping sourceType="NETSUITE:NETSUITE_ERROR" targetType="CUSTOMNS:RECORD_DOES_NOT_EXIST" />
			<reconnect frequency="${netsuite.retry.timePeriod}" count="${netsuite.retry.maxRetries}" />
			<netsuite:message><![CDATA[#[vars.vRetrieveProductBody]]]></netsuite:message>
		</netsuite:get>
	</sub-flow>
</mule>
