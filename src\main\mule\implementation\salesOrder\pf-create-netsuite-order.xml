<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:validation="http://www.mulesoft.org/schema/mule/validation" xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:netsuite="http://www.mulesoft.org/schema/mule/netsuite" xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/netsuite http://www.mulesoft.org/schema/mule/netsuite/current/mule-netsuite.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/validation http://www.mulesoft.org/schema/mule/validation/current/mule-validation.xsd">
	<flow name="pf-create-netsuite-order" doc:id="906bd652-c47d-4c1e-a247-1f1606536524" >
		
		<logger level="INFO" doc:name="LOG INFO: Log Entry" doc:id="d5458868-cb4f-4024-96d6-d602a96cb555" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-create-netsuite-order", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Inbound Request" doc:id="6e0349d1-32f0-41fd-85ce-79696371d1ba" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Inbound Request",&#10;	"FlowName" : "pf-create-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="DEBUG" doc:name="LOG DEBUG: Log Inound Request Payload" doc:id="74259eb4-993b-4ced-b675-24bf82cfb61c" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Inbound Request Payload",&#10;	"FlowName" : "pf-create-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendRequest": payload,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<try doc:name="Try" doc:id="c2ab8645-8769-4ae8-b06e-c82d6871df85">
			<set-variable value='#["0" as Number]' doc:name="vRetryCount" doc:id="36133f1b-b77f-4fbb-aa14-96e47eb61c86" variableName="vRetryCount" />
			<until-successful maxRetries="${netsuite.retry.maxRetries}" doc:name="Until Successful" doc:id="505ef7f6-5e30-484d-83fb-aab1e2d3c03c" millisBetweenRetries="${netsuite.retry.timePeriod}">
				<try doc:name="Try" doc:id="d7954c10-94d5-498f-aea1-b900a3dc6850">
					<choice doc:name="Is BillingScheduleDetail required" doc:id="d75957d7-9b6c-4420-a199-4a8ff20d94d1" >
					<when expression="#[!isEmpty(flatten(flatten(payload.order.orderItems.billingSchedule).billingScheduleDetails))]">
						<ee:transform doc:name="Set vCreateBillingScheduleDetailsBody" doc:id="4ca3339e-816e-4a6f-9e82-cdc37a305339" >
							<ee:message >
							</ee:message>
							<ee:variables >
								<ee:set-variable resource="dwl/create/createNetsuiteBillingScheduleDetailsMapping.dwl" variableName="vCreateBillingScheduleDetailsBody" />
							</ee:variables>
						</ee:transform>
						<logger level="DEBUG" doc:name="LOG DEBUG: Before BillingScheduleDetails create" doc:id="2b9ff6e2-9c12-4bb8-a115-ef599a511f01" message='#[%dw 2.0&#10;output application/json writeAttributes=true&#10;---&#10;{ 	&#10;	"Message" : "Before BillingScheduleDetail create",&#10;	"FlowName" : "pf-create-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendRequest": vars.vCreateBillingScheduleDetailsBody,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
						<netsuite:add-list doc:name="Add BillingScheduleDetails" doc:id="f84f081e-a9a8-472a-aa13-6346bd8c4acb" config-ref="NetSuite_Config" type="#[p('netsuite.custRecord.billingScheduleDetails.type')]" target="vCreateBillingScheduleDetailsResponse" >
							<netsuite:message ><![CDATA[#[vars.vCreateBillingScheduleDetailsBody]]]></netsuite:message>
						</netsuite:add-list>
						<logger level="DEBUG" doc:name="LOG DEBUG: After BillingScheduleDetails create" doc:id="b0f46142-2bf3-42c6-8303-72ab1004feab" message='#[%dw 2.0&#10;output application/json writeAttributes=true&#10;---&#10;{ 	&#10;	"Message" : "After BillingScheduleDetail create",&#10;	"FlowName" : "pf-create-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendRequest": vars.vCreateBillingScheduleDetailsResponse,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
						<try doc:name="Try" doc:id="e3c59b76-2292-4aed-9742-bcde2d464c46" >
							<validation:is-true doc:name="Are BillingScheduleDetails created?" doc:id="9f92d10c-9630-4cde-a1ac-36d5be9664ef" expression='#[%dw 2.0 import * from dw::core::Arrays output application/json --- (vars.vCreateBillingScheduleDetailsResponse.addListResponse.writeResponseList.*writeResponse.status.@isSuccess default []) every (($) ~= "true")]' message='#["Error while creating BillingScheduleDetails in Netsuite"]' />
							<error-handler >
								<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="40722f6e-3749-4b27-8bed-ced735da96f2" >
									<set-variable value='#[%dw 2.0&#10;import * from dw::core::Arrays&#10;output application/json&#10;var netsuiteError = (vars.vCreateBillingScheduleDetailsResponse.addListResponse.writeResponseList.*writeResponse.status filter(($).@isSuccess ~= "false") default [])[0].statusDetail.message&#10;---&#10;{&#10;	"message": "Error occurred while creating BillingScheduleDetails",&#10;	"details": netsuiteError&#10;}]' doc:name="vError" doc:id="d7c3dcc7-67a7-495e-a43a-e45a4e82199e" variableName="vError" />
									<raise-error doc:name="CUSTOM:DATA_VALIDATION_ERROR" doc:id="562ee390-e1be-444a-8eaf-3bece774d934" type="CUSTOM:DATA_VALIDATION_ERROR" />
								</on-error-propagate>
							</error-handler>
						</try>
					</when>
					<otherwise >
						<logger level="INFO" doc:name="LOG INFO: No BillingScheduleDetail sent" doc:id="0ba8529f-74f0-4bc2-9d8e-8e281601f593" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "No BillingScheduleDetail is passed for creating " ++ (payload.order.sfId) ++ " Order from Salesforce.",&#10;	"FlowName" : "pf-create-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
					</otherwise>
				</choice>
				<choice doc:name="Is BillingSchedule required" doc:id="659c40c3-b3fc-45e6-829a-eee644e66129">
								<when expression="#[!isEmpty(payload.order.orderItems.billingSchedule)]">
									<ee:transform doc:name="Set vCreateBillingSchedulesBody" doc:id="4bec57b8-9a3c-4103-a710-e950917b8551">
										<ee:message>
										</ee:message>
										<ee:variables>
									<ee:set-variable resource="dwl/create/createNetsuiteBillingSchedulesMapping.dwl" variableName="vCreateBillingSchedulesBody" />
										</ee:variables>
									</ee:transform>
							<logger level="DEBUG" doc:name="LOG DEBUG: Before BillingSchedule create" doc:id="a031b815-c7bc-49e8-b7b4-f8f5bfa2e460" message='#[%dw 2.0&#10;output application/json writeAttributes=true&#10;---&#10;{ 	&#10;	"Message" : "Before BillingSchedule create",&#10;	"FlowName" : "pf-create-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendRequest": vars.vCreateBillingSchedulesBody,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]'/>
							<netsuite:add-list doc:name="Add BillingSchedules" doc:id="f514cae9-e1e9-425a-98d6-f156f64e1a7a" config-ref="NetSuite_Config" type="billingSchedule" target="vCreateBillingSchedulesResponse">
								<netsuite:message ><![CDATA[#[vars.vCreateBillingSchedulesBody]]]></netsuite:message>
							</netsuite:add-list>
							<logger level="DEBUG" doc:name="LOG DEBUG: After BillingSchedule create" doc:id="2d0888b9-dbbb-4e34-93d4-07bfa97ff38e" message='#[%dw 2.0&#10;output application/json writeAttributes=true&#10;---&#10;{ 	&#10;	"Message" : "After BillingSchedule create",&#10;	"FlowName" : "pf-create-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendRequest": vars.vCreateBillingSchedulesResponse,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]'/>
							<try doc:name="Try" doc:id="249ac1fd-ec23-41b1-986c-2112b9333ec5" >
								<validation:is-true doc:name="Are BillingSchedules created?" doc:id="d501f96d-1589-425f-9a1d-bdd1e9d5453f" expression='#[%dw 2.0 import * from dw::core::Arrays output application/json --- (vars.vCreateBillingSchedulesResponse.addListResponse.writeResponseList.*writeResponse.status.@isSuccess default []) every (($) ~= "true")]' message='#["Error while creating BillingSchedules in Netsuite"]' />
								<error-handler >
									<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="b2ae9ff3-3058-4978-9862-238f8a71a7ae" >
										<set-variable value='#[%dw 2.0&#10;import * from dw::core::Arrays&#10;output application/json&#10;var netsuiteError = (vars.vCreateBillingSchedulesResponse.addListResponse.writeResponseList.*writeResponse.status filter(($).@isSuccess ~= "false") default [])[0].statusDetail.message&#10;---&#10;{&#10;	"message": "Error occurred while creating BillingSchedules",&#10;	"details": netsuiteError&#10;}]' doc:name="vError" doc:id="28ea9a51-64b5-4b3d-bb95-c03f64790686" variableName="vError"/>
										<raise-error doc:name="CUSTOM:DATA_VALIDATION_ERROR" doc:id="8653e374-a072-4a23-b20e-8acfe80e652b" type="CUSTOM:DATA_VALIDATION_ERROR"/>
									</on-error-propagate>
								</error-handler>
							</try>
							<!-- [STUDIO:"LOG INFO: BillingSchedules created"]<logger level="INFO" doc:name="LOG INFO: BillingSchedules created" doc:id="9a72c537-75c3-4223-b742-8934db8ea9fc" message='#[%dw 2.0&#10;output application/json&#10;&#45;&#45;- &#10;{&#10;  	"BusinessKey" : vars.vBusinessKey,&#10;  	"Message" : (sizeOf(payload.order.orderItems.billingSchedule) default "-1") ++ " BillingSchedules created", &#10;	"FlowName" : "pf-create-netsuite-order", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}&#93;' /> [STUDIO] -->
								</when>
								<otherwise>
									<logger level="INFO" doc:name="LOG INFO: No BillingSchedule sent" doc:id="74113353-5a6d-4711-a34b-d7de58078372" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "No BillingSchedule is passed for creating " ++ (payload.order.sfId) ++ " Order from Salesforce.",&#10;	"FlowName" : "pf-create-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
								</otherwise>
							</choice>
					<ee:transform doc:name="Set vCreateOrderBody" doc:id="af64fe32-e998-4d36-a868-4a0c9dcfdec2">
			<ee:message>
			</ee:message>
			<ee:variables>
				<ee:set-variable resource="dwl/create/createNetsuiteOrderMapping.dwl" variableName="vCreateOrderBody" />
			</ee:variables>
		</ee:transform>
					<logger level="DEBUG" doc:name="LOG DEBUG: Before SalesOrder create" doc:id="65d8de6c-2f0c-4476-b891-793b8f49fd09" message='#[%dw 2.0&#10;output application/json writeAttributes=true&#10;---&#10;{ 	&#10;	"Message" : "Before SalesOrder create",&#10;	"FlowName" : "pf-create-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendRequest": vars.vCreateOrderBody,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
					<netsuite:add doc:name="Add SalesOrder" doc:id="f50db506-8373-42a6-ab54-e8f27f4aad5c" config-ref="NetSuite_Config" type="salesOrder" target="vCreateOrderResponse">
						<netsuite:message><![CDATA[#[vars.vCreateOrderBody]]]></netsuite:message>
					</netsuite:add>
					<logger level="DEBUG" doc:name="LOG DEBUG: After SalesOrder create" doc:id="6fb3878a-1869-4d95-abbc-016d546c8e7b" message='#[%dw 2.0&#10;output application/json writeAttributes=true&#10;---&#10;{ 	&#10;	"Message" : "After SalesOrder create",&#10;	"FlowName" : "pf-create-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendRequest": vars.vCreateOrderResponse,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]'/>
					<try doc:name="Try" doc:id="b5f24c33-1f5b-4fb0-ae39-8c3f38e3042c" >
						<validation:is-true doc:name="Is SalesOrder created?" doc:id="83703a00-e80b-4226-8dd6-d4bc1ea400c6" expression='#[output application/json --- vars.vCreateOrderResponse.addResponse.writeResponse.status.@isSuccess ~= "true"]' message='#["Error occurred while creating SalesOrder in Netsuite"]' />
						<error-handler >
							<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="90c92e0d-5c4f-4cfd-848c-08943d9085e1" >
								<set-variable value='#[%dw 2.0&#10;import * from dw::core::Arrays&#10;output application/json&#10;var netsuiteError = (vars.vCreateOrderResponse.addResponse.writeResponse.status.statusDetail.message)&#10;---&#10;{&#10;	"message": "Error occurred while creating SalesOrder",&#10;	"details": netsuiteError&#10;}]' doc:name="vError" doc:id="83e39591-608c-469f-b407-d8c04f6db1b1" variableName="vError" />
								<raise-error doc:name="CUSTOM:DATA_VALIDATION_ERROR" doc:id="7ce026cf-b42f-4250-83eb-21ef052f9e01" type="CUSTOM:DATA_VALIDATION_ERROR" />
							</on-error-propagate>
						</error-handler>
					</try>
					<ee:transform doc:name="Set vRetrieveOrderBody, vRetrieveBillingSchedulesBody, vRetrieveBillingScheduleDetailsBody" doc:id="76b18ea5-6df8-4c38-a47f-d62dff723d81">
				<ee:message />
				<ee:variables>
					<ee:set-variable variableName="vRetrieveBillingSchedulesBody"><![CDATA[%dw 2.0
output application/xml
ns ns0 urn:messages_2021_1.platform.webservices.netsuite.com
ns ns01 urn:common_2021_1.platform.webservices.netsuite.com
ns ns02 urn:core_2021_1.platform.webservices.netsuite.com
ns ns03 urn:relationships_2021_1.platform.webservices.netsuite.com
ns ns04 urn:sales.transactions.webservices.netsuite.com
ns xsi http://www.w3.org/2001/XMLSchema-instance
---
{
	ns0#getList: ((vars.vCreateBillingSchedulesResponse.addListResponse.writeResponseList.*writeResponse.baseRef.@internalId default []) map() -> {
		ns01#baseRef @("xmlns:ns0": ns0, "xmlns:ns02": ns02, xsi#"type": "ns02:RecordRef", "internalId": ($), "type": "billingSchedule"): ""
	}) reduce ($$ ++ $)
}]]></ee:set-variable>
					<ee:set-variable variableName="vRetrieveOrderBody"><![CDATA[%dw 2.0
output application/xml
ns ns0 urn:messages_2021_1.platform.webservices.netsuite.com
ns ns01 urn:common_2021_1.platform.webservices.netsuite.com
ns ns02 urn:core_2021_1.platform.webservices.netsuite.com
ns ns03 urn:relationships_2021_1.platform.webservices.netsuite.com
ns ns04 urn:sales.transactions.webservices.netsuite.com
ns xsi http://www.w3.org/2001/XMLSchema-instance
---
{
	ns0#get: {
		ns0#baseRef @("xmlns:ns0": ns0, "xmlns:ns02": ns02, xsi#"type": "ns02:RecordRef", "internalId": vars.vCreateOrderResponse.addResponse.writeResponse.baseRef.@internalId, "type": "salesOrder"): ""
	}
}]]></ee:set-variable>
							<ee:set-variable variableName="vRetrieveBillingScheduleDetailsBody" ><![CDATA[%dw 2.0
output application/xml
ns ns0 urn:messages.platform.webservices.netsuite.com
ns ns01 urn:core.platform.webservices.netsuite.com
ns xsi http://www.w3.org/2001/XMLSchema-instance
---
{
	ns0#getList: ((vars.vCreateBillingScheduleDetailsResponse.addListResponse.writeResponseList.*writeResponse.baseRef.@internalId default []) map() -> {
		ns01#baseRef @(internalId: ($) , "typeId": p('netsuite.custRecord.billingScheduleDetails.internalId'), xsi#"type": "ns01:CustomRecordRef"): null
	}) reduce ($$ ++ $)
}]]></ee:set-variable>
				</ee:variables>
			</ee:transform>
					<scatter-gather doc:name="Fetch records" doc:id="2d28fd0f-1aa2-4583-9e65-67066473bf2f" target="vSGFetchResponse">
				<route>
					<netsuite:get doc:name="Get SalesOrder" doc:id="f219c020-7735-4ccb-8c7a-a714bd54a3fd" config-ref="NetSuite_Config" refType="RecordRef" type="salesOrder">
						<netsuite:message><![CDATA[#[vars.vRetrieveOrderBody]]]></netsuite:message>
					</netsuite:get>
				</route>
				<route>
					<choice doc:name="Is BillingSchedule required" doc:id="56943019-585f-4891-8960-e74eed2d8c66">
						<when expression="#[!isEmpty(payload.order.orderItems.billingSchedule)]">
							<netsuite:get-list doc:name="Get BillingSchedules" doc:id="8cfdeab0-79f7-4695-92e7-f2b68c16f002" config-ref="NetSuite_Config" refType="RecordRef" type="billingSchedule">
						<netsuite:message><![CDATA[#[vars.vRetrieveBillingSchedulesBody]]]></netsuite:message>
					</netsuite:get-list>
						</when>
					</choice>
				</route>
						<route >
							<choice doc:name="Is BillingScheduleDetail required" doc:id="1a90f0ce-25dc-45eb-b14f-88b8ccde9ad7">
						<when expression="#[!isEmpty(flatten(flatten(payload.order.orderItems.billingSchedule).billingScheduleDetails))]">
									<netsuite:get-list doc:name="Get BillingScheduleDetails" doc:id="c1ea69f2-e728-470b-80e0-0a993d6cb281" config-ref="NetSuite_Config">
										<netsuite:message ><![CDATA[#[vars.vRetrieveBillingScheduleDetailsBody]]]></netsuite:message>
									</netsuite:get-list>
								</when>
					</choice>
						</route>
			</scatter-gather>
					<error-handler>
						<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="94ea2f9f-31b4-492e-a812-a33debb066eb" type="NETSUITE:CONNECTIVITY, NETSUITE:SESSION_TIMED_OUT">
							<set-variable value="#[(vars.vRetryCount as Number) + 1]" doc:name="Update vRetryCount" doc:id="cdabf904-9e4b-4da2-8fc7-5f2a0cce02e2" variableName="vRetryCount" />
						</on-error-propagate>
						<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="c02e54e1-cb58-4934-aaea-a5307b1fbcb5" type="NETSUITE:INVALID_VERSION, NETSUITE:NETSUITE_ERROR, NETSUITE:NETSUITE_SOAP_FAULT, NETSUITE:USER_ERROR, CUSTOM:DATA_VALIDATION_ERROR">
							<ee:transform doc:name="Set vCreateFlag, vErrorBody" doc:id="6ad658b9-16d0-4fa5-9902-4d5685690a90" >
								<ee:message >
								</ee:message>
								<ee:variables >
									<ee:set-variable variableName="vCreateFlag" ><![CDATA[false]]></ee:set-variable>
									<ee:set-variable variableName="vErrorBody" ><![CDATA[%dw 2.0
output application/json
---
if(vars.vError != null) // custom error
{
	"message": vars.vError.message,
	"details": vars.vError.details
}
else 
{
	"message": "Error occurred while creating SalesOrder/BillingSchedules",
	"details": error.description	
}]]></ee:set-variable>
								</ee:variables>
							</ee:transform>
						</on-error-continue>
					</error-handler>
				</try>
				<choice doc:name="SalesOrder Created?" doc:id="c44486d2-9268-453d-9d11-a10c55592237" >
					<when expression="#[output application/json&#10;---&#10;((!isEmpty(vars.vSGFetchResponse.'0'.payload.getResponse.readResponse.record)) and (!isEmpty(vars.vSGFetchResponse.'2'.payload.getListResponse.readResponseList.*readResponse)))]">
						<ee:transform doc:name="Set vBillingScheduleDetailsBody" doc:id="9a42d13b-1c62-4c92-bd4b-a4281534f422">
						<ee:variables>
							<ee:set-variable resource="dwl/create/updateNetsuiteBillingScheduleDetails.dwl" variableName="vBillingScheduleDetailsBody" />
						</ee:variables>
					</ee:transform>
						<logger level="DEBUG" doc:name="LOG DEBUG: Before BillingScheduleDetails Update" doc:id="b33ce47e-5e14-4627-aa9c-db326c2b0b3c" message='#[%dw 2.0&#10;output application/json writeAttributes=true&#10;---&#10;{ 	&#10;	"Message" : "Before BillingScheduledDetails update",&#10;	"FlowName" : "pf-create-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendRequest": vars.vBillingScheduleDetailsBody,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
						<netsuite:async-update-list doc:name="Async update list" doc:id="388a06d9-4d49-4ccd-8d7a-d3436e7d0c21" config-ref="NetSuite_Config" target="vBillingScheduleDetailsResponse" type="#[p('netsuite.custRecord.billingScheduleDetails.type')]">
					<netsuite:message><![CDATA[#[vars.vBillingScheduleDetailsBody]]]></netsuite:message>
				</netsuite:async-update-list>
						<logger level="DEBUG" doc:name="LOG DEBUG: After BillingScheduleDetails Update" doc:id="0ebd633d-9a3f-4f62-bcea-0d1eb4862a73" message='#[%dw 2.0&#10;output application/json writeAttributes=true&#10;---&#10;{ 	&#10;	"Message" : "After BillingScheduledDetails update",&#10;	"FlowName" : "pf-create-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendRequest": vars.vBillingScheduleDetailsResponse,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
					</when>
				</choice>
				<set-variable value="#[true]" doc:name="vCreateFlag" doc:id="0b644d84-656a-40f2-8020-6a8e6ce6123b" variableName="vCreateFlag" />
			</until-successful>
			<ee:transform doc:name="Set payload, httpStatus" doc:id="b73aa098-4498-4595-89bb-6a85bedb7b12">
				<ee:message>
					<ee:set-payload><![CDATA[%dw 2.0
import * from dw::core::Strings
var orderRecord = vars.vSGFetchResponse.'0'.payload.getResponse.readResponse.record default {}
var billingScheduleRecords = vars.vSGFetchResponse.'1'.payload.getListResponse.readResponseList.*readResponse default []
var billingScheduleDetailRecords = vars.vSGFetchResponse.'2'.payload.getListResponse.readResponseList.*readResponse default []
var inputBSDetails = (flatten(flatten(payload.order.orderItems.billingSchedule).billingScheduleDetails))
output application/json writeAttributes=true
---
if(!isEmpty(orderRecord)) {
	"code": 201,
    "status": "SUCCESS",
    "transactionId": vars.vTransactionId,
	"response": {
        "order": {
			"sfId": orderRecord.customFieldList.StringCustomFieldRef__custbody_salesforce_id.value default null,
			"netsuiteId": orderRecord.@internalId default null,
			"netsuiteOrderNumber": orderRecord.tranId default null,
            
            ("orderItems": [] ++ (
                orderRecord.itemList.*item map(v, i) -> {
                	"sfId": (v).customFieldList.StringCustomFieldRef__custcol_salesforce_id.value default null,
                	"nsId": (v).line default null,
                	
                	"billingSchedule": do {
                		var thisBS = (billingScheduleRecords filter(($).record.@internalId ~= ((v).billingSchedule.@internalId)))[0].record
                		var thisBSSfId = substringAfter((thisBS).name, "Salesforce Id: ")
                		var billingScheduleDates = (
                			thisBS.recurrenceList.*billingScheduleRecurrence map() -> do {
	                			var thisOI = (payload.order.orderItems filter(($).sfId ~= (v).customFieldList.StringCustomFieldRef__custcol_salesforce_id.value))[0]
	                			---
	                			{
		                			"sfId": (
		                				if(!isEmpty(thisOI)) (
		                					thisOI.billingSchedule.billingScheduleDates[$$].sfId
		                				) else null
		                			),
		                			"nsId": ($).recurrenceId default null,
		                			"date": ($).recurrenceDate default null
	                			}
	                		}
                		)
                		---
                		{	
	                		"sfId": thisBSSfId default null, // thisBS.customFieldList.StringCustomFieldRef__custcol_salesforce_id.value default null,
	                		"nsId": (v).billingSchedule.@internalId default null,
	                		
	                		"billingScheduleDates": billingScheduleDates default [] map() -> ($ filterObject(!($$ ~= "date"))),
		                	
		                	"billingScheduleDetails": do {
		                		// bsdetail's name would be same as SfId of BillingSchedule + Date of BillingScheduleDate
			                	// BSDetail: "bsid123-23/03/2022" // BS: "bsid123" // BSDate's date: 23/03/2022
			                	var thisOI = (payload.order.orderItems filter(($).sfId ~= (v).customFieldList.StringCustomFieldRef__custcol_salesforce_id.value))[0]
			                	var billingScheduleDateList = billingScheduleDates.date map() -> (($) as Date as String {format: 'yyyy-MM-dd'}) default []
			                	var billingScheduleDetails = billingScheduleDetailRecords filter(
			                		billingScheduleDateList contains (substringAfter(($).record.name, (thisBS.name ++ "-")))
			                	)
			                	---
				               	billingScheduleDetailRecords filter(billingScheduleDateList contains (substringAfter(($).record.name, ((thisBS.name default "") ++ "-")))) map(bsDetailV, bsDetailK) -> {
			                		"bsDateSfId": (
		                				if(!isEmpty(thisOI)) (
		                					thisOI.billingSchedule.billingScheduleDates[bsDetailK].sfId
		                				) else null
		                			), // (bsDetailV).record.customFieldList.StringCustomFieldRef__custcol_billing_schedule_date_salesforce_id.value default null
			                		"nsId": (bsDetailV).record.@internalId default null
			                	}
			                }	
			            }
		            }
                }
            ) - {}) if(!isEmpty(orderRecord.itemList.*item))
        }
    }
}
else {
    "code": 400,
    "status": "FAILURE",
    "transactionId": vars.vTransactionId,
    "response": vars.vErrorBody
}]]></ee:set-payload>
				</ee:message>
				<ee:variables>
					<ee:set-variable variableName="httpStatus"><![CDATA[%dw 2.0
var orderRecord = vars.vSGFetchResponse.'0'.payload.getResponse.readResponse.record default {}
var billingScheduleRecords = vars.vSGFetchResponse.'1'.payload.getListResponse.readResponseList.*readResponse default []
output application/json
---
if(!isEmpty(orderRecord)) 201 else 400  ]]></ee:set-variable>
				</ee:variables>
			</ee:transform>
			<error-handler>
				<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="db67ce85-940a-4bcf-aa6e-2702125b4c0c" type="ANY">
					<logger level="ERROR" doc:name="LOG ERROR: Exception while creating SalesOrder" doc:id="fb2def61-b1cf-4da3-b88f-49ec7a6f403a" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Exception while creating SalesOrder/BillingSchedule", &#10;	"FlowName" : "pf-create-netsuite-order", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"ErrorDescription": error.description,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]'/>
				</on-error-propagate>
			</error-handler>
		</try>
		<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Response Payload" doc:id="0aecb305-1c87-4657-bde6-6d3e34966c33" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-create-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendResponse": payload,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response" doc:id="64ef6e73-1e38-4b25-917f-beead7ac9d00" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-create-netsuite-order",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit" doc:id="0d76ada3-cde3-400c-aa8d-0fe3d4467c7b" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-create-netsuite-order",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
	</flow>
</mule>
