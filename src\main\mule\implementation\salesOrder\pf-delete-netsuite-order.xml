<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:netsuite="http://www.mulesoft.org/schema/mule/netsuite"
	xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/netsuite http://www.mulesoft.org/schema/mule/netsuite/current/mule-netsuite.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd">
	<flow name="pf-delete-netsuite-order" doc:id="647970f2-0d34-4fe2-ba4d-9a6859d3059a" >
		
		<logger level="INFO" doc:name="LOG INFO: Log Entry" doc:id="d0780f6d-8527-43f3-a57d-45b359af87cb" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-delete-netsuite-order", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Inbound Request" doc:id="3fed77b5-3727-41b5-b8f5-a185f178bc18" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Inbound Request",&#10;	"FlowName" : "pf-delete-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="DEBUG" doc:name="LOG DEBUG: Log Inbound Request Payload" doc:id="3c63f56a-4b5f-4063-b8bd-c53fdfe7c5af" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Inbound Request Payload",&#10;	"FlowName" : "pf-delete-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendRequest": payload,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<ee:transform doc:name="Set vInternalId, vBillingScheduleIds, vBillingScheduleDetailIds" doc:id="e3a79014-6f16-4906-afcc-fb9e6ee1e0af" >
			<ee:message />
			<ee:variables >
				<ee:set-variable variableName="vInternalId" ><![CDATA[%dw 2.0
output application/json
---
attributes.queryParams.'orderNSId']]></ee:set-variable>
				<ee:set-variable variableName="vBillingScheduleIds" ><![CDATA[%dw 2.0
output application/json
---
attributes.queryParams.'billingScheduleNSIds' splitBy ","]]></ee:set-variable>
				<ee:set-variable variableName="vBillingScheduleDetailIds" ><![CDATA[%dw 2.0
output application/json
---
attributes.queryParams.'billingScheduleDetailNSIds' splitBy ","]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<ee:transform doc:name="Set vDeleteOrderBody, vDeleteBillingScheduleBody" doc:id="faa71b15-ba05-4c24-aa7a-53c7ba0be9e8" >
			<ee:message />
			<ee:variables >
				<ee:set-variable resource="dwl/delete/deleteNetsuiteOrderMapping.dwl" variableName="vDeleteOrderBody" />
				<ee:set-variable resource="dwl/delete/deleteNetsuiteBillingScheduleMapping.dwl" variableName="vDeleteBillingSchedulesBody" />
				<ee:set-variable variableName="vDeleteBillingScheduleDetailsBody" ><![CDATA[%dw 2.0
output application/xml
ns ns0 urn:messages.platform.webservices.netsuite.com
ns ns01 urn:core.platform.webservices.netsuite.com
ns xsi http://www.w3.org/2001/XMLSchema-instance
---
{
	ns0#deleteList: (vars.vBillingScheduleDetailIds map() -> {
		ns01#baseRef @(internalId: ($) , "typeId": p('netsuite.custRecord.billingScheduleDetails.internalId'), xsi#"type": "ns01:CustomRecordRef"): null
	}) reduce($$ ++ $)
}]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<try doc:name="Try" doc:id="77185237-b848-4598-9ae2-c8f40ffedd56" >
			<set-variable value='#["0" as Number]' doc:name="vRetryCount" doc:id="c708fea3-7e3c-4863-a348-c5a65ec6bf50" variableName="vRetryCount" />
			<until-successful maxRetries="${netsuite.retry.maxRetries}" doc:name="Until Successful" doc:id="0c4a9dcc-b1d6-4215-bb6b-fc651252d8c3" millisBetweenRetries="${netsuite.retry.timePeriod}" >
				<try doc:name="Try" doc:id="22e0b8b4-a295-4fe4-b7c7-2de7824153f4" >
					<netsuite:delete doc:name="Delete SalesOrder" doc:id="cec9d1d7-7d89-42eb-93ab-e689df784074" config-ref="NetSuite_Config" refType="RecordRef" type="salesOrder" target="vDeleteOrderResponse">
						<netsuite:message><![CDATA[#[vars.vDeleteOrderBody]]]></netsuite:message>
					</netsuite:delete>
					<netsuite:delete-list doc:name="Delete BillingSchedules" doc:id="bee34d2b-a552-4369-a6fe-a8a154326c95" config-ref="NetSuite_Config" type="billingSchedule" refType="RecordRef" target="vDeleteBillingSchedulesResponse">
								<netsuite:message><![CDATA[#[vars.vDeleteBillingSchedulesBody]]]></netsuite:message>
					</netsuite:delete-list>
					<netsuite:delete-list doc:name="Delete BillingScheduleDetails" doc:id="dc0fc052-ee10-4171-8128-ec2ba65250a4" config-ref="NetSuite_Config" target="vDeleteBillingScheduleDetailsResponse">
						<netsuite:message><![CDATA[#[vars.vDeleteBillingScheduleDetailsBody]]]></netsuite:message>
					</netsuite:delete-list>
					<set-variable value="#[true]" doc:name="vDeleteFlag" doc:id="2c60a150-c952-47f7-998b-35a67207e737" variableName="vDeleteFlag"/>
					<error-handler >
						<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="4bc0a96f-23aa-4503-a76d-059a12cac3f0" type="NETSUITE:CONNECTIVITY, NETSUITE:SESSION_TIMED_OUT" >
							<set-variable value="#[(vars.vRetryCount as Number) + 1]" doc:name="Update vRetryCount" doc:id="7d9073f1-eeba-48a1-a007-b52264f0bc4d" variableName="vRetryCount" />
						</on-error-propagate>
						<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="f94e671b-cffd-408c-b32c-a9d15efdc924" type="NETSUITE:INVALID_VERSION, NETSUITE:NETSUITE_ERROR, NETSUITE:NETSUITE_SOAP_FAULT, NETSUITE:USER_ERROR" >
							<ee:transform doc:name="Set vDeleteFlag, vError" doc:id="d0877b66-d815-4012-a11a-90ebe3d0a214" >
								<ee:message />
								<ee:variables >
									<ee:set-variable variableName="vError" ><![CDATA[%dw 2.0
output application/json
---
{
	"description": error.description
}]]></ee:set-variable>
									<ee:set-variable variableName="vDeleteFlag" ><![CDATA[false]]></ee:set-variable>
								</ee:variables>
							</ee:transform>
						</on-error-continue>
					</error-handler>
				</try>
			</until-successful>
			<ee:transform doc:name="Set payload, httpStatus" doc:id="046c1e2b-b248-4107-84e9-468456a3ecab" >
				<ee:message >
					<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
if(vars.vDeleteFlag) {
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "SUCCESS",
  "response": {
    "internalId": vars.vInternalId,
    "message": "Order " ++ (vars.vInternalId default "-1") ++ " has been deleted successfully"
  }
}
else {
  "code": 400,
  "transactionId": vars.vTransactionId,
  "status": "FAILURE",
  "response": {
      "message": "Error occurred while deleting SalesOrder/BillingSchedules/BillingScheduleDetails in Netsuite",
      "details": vars.vError.description
  }
}
]]></ee:set-payload>
				</ee:message>
				<ee:variables >
					<ee:set-variable variableName="httpStatus" ><![CDATA[output application/json
---
if(vars.vDeleteFlag) 200 else 400]]></ee:set-variable>
				</ee:variables>
			</ee:transform>
			<error-handler >
				<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="8b8b8d8c-7c5b-4ee8-adb3-f8e1203fdcdc" type="ANY" >
					<logger level="ERROR" doc:name="LOG ERROR: Exception while deleting SalesOrder" doc:id="c5bfb06d-d1c0-4579-8a6d-de6a8cec3e6d" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Exception while deleting SalesOrder/BillingSchedule/BillingScheduleDetail", &#10;	"FlowName" : "pf-delete-netsuite-order", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"ErrorDescription": error.description,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
				</on-error-propagate>
			</error-handler>
		</try>
		<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Response Payload" doc:id="d7d5f02e-fd41-4741-b8b6-ecafa2312e07" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-delete-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendResponse": payload,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response" doc:id="6a2e01c7-7494-49ce-ba69-f22317d144ae" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-delete-netsuite-order",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit" doc:id="e17d03a3-f4cc-4d69-8805-4ed11add27c5" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-delete-netsuite-order",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
	</flow>
</mule>
