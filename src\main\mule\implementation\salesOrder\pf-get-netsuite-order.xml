<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:slack="http://www.mulesoft.org/schema/mule/slack" xmlns:validation="http://www.mulesoft.org/schema/mule/validation"
	xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core" xmlns:netsuite="http://www.mulesoft.org/schema/mule/netsuite" xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/netsuite http://www.mulesoft.org/schema/mule/netsuite/current/mule-netsuite.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/validation http://www.mulesoft.org/schema/mule/validation/current/mule-validation.xsd
http://www.mulesoft.org/schema/mule/slack http://www.mulesoft.org/schema/mule/slack/current/mule-slack.xsd">
	<flow name="pf-get-netsuite-order" doc:id="40d8078b-0e53-42de-94fc-d2cd25ac4de7" >
		<logger level="INFO" doc:name="LOG INFO: Log Entry" doc:id="a8c6e579-6a69-42c0-8f8d-bbb9603151ab" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-get-netsuite-order", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Inbound Request" doc:id="e9390d1e-eff6-4ee7-89a5-2123fbc922f4" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Inbound Request",&#10;	"FlowName" : "pf-get-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="DEBUG" doc:name="LOG DEBUG: Log Inbound Request Payload" doc:id="542889a3-f231-42a6-bcf4-29ed7a4e5323" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Inbound Request Payload",&#10;	"FlowName" : "pf-get-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendRequest": payload,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<ee:transform doc:name="Set vInternalId, vRetrieveOrderBody, vRetrieveBillingSchedulesBody" doc:id="8edc6017-64ad-4a26-a253-bebbb03d9f7f" >
			<ee:message />
			<ee:variables >
				<ee:set-variable variableName="vInternalId" ><![CDATA[%dw 2.0
output application/json
---
attributes.queryParams.'orderNSId' default ""]]></ee:set-variable>
				<ee:set-variable resource="dwl/read/readNetsuiteBillingSchedulesMapping.dwl" variableName="vRetrieveBillingSchedulesBody" />
				<ee:set-variable resource="dwl/read/readNetsuiteOrderMapping.dwl" variableName="vRetrieveOrderBody" />
				<ee:set-variable variableName="vRetrieveBillingScheduleDetailsBody" ><![CDATA[%dw 2.0
output application/xml
ns ns0 urn:messages.platform.webservices.netsuite.com
ns ns01 urn:core.platform.webservices.netsuite.com
ns xsi http://www.w3.org/2001/XMLSchema-instance
---
{
	ns0#getList: ((attributes.queryParams.'billingScheduleDetailIds' splitBy ",") map() -> {
		ns01#baseRef @(internalId: ($) , "typeId": p('netsuite.custRecord.billingScheduleDetails.internalId'), xsi#"type": "ns01:CustomRecordRef"): null
	}) reduce ($$ ++ $)
}]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<scatter-gather doc:name="Fetch records" doc:id="f280e8db-bd62-4aa7-8be3-a4a72aaf078b" target="vSGFetchResponse">
			<route>
				<try doc:name="Try" doc:id="262b6f03-9286-43f5-b291-983c9389a051" >
					<netsuite:get doc:name="Get SalesOrder" doc:id="9929fd36-ad12-40b4-913f-fecfb731047b" config-ref="NetSuite_Config" refType="RecordRef" type="salesOrder">
			<netsuite:message><![CDATA[#[vars.vRetrieveOrderBody]]]></netsuite:message>
		</netsuite:get>
					<error-handler >
						<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="6c1235f3-d762-452d-aad3-b49252d506f3" type="ANY">
							<ee:transform doc:name="Set payload" doc:id="fb8a053d-f87f-4044-8c2f-399e390d0a8f" >
								<ee:message >
									<ee:set-payload ><![CDATA[output application/json --- error.'errorMessage'.'payload']]></ee:set-payload>
								</ee:message>
								<ee:variables >
								</ee:variables>
							</ee:transform>
						</on-error-continue>
					</error-handler>
				</try>
			</route>
			<route>
				<try doc:name="Try" doc:id="fca92c6b-4521-4638-9e43-2c61997ecef8" >
					<netsuite:get-list doc:name="Get BillingSchedules" doc:id="ccd8fedd-e4b6-4fe5-bd2f-5e606bf9d39c" config-ref="NetSuite_Config" refType="RecordRef" type="billingSchedule">
					<netsuite:message><![CDATA[#[vars.vRetrieveBillingSchedulesBody]]]></netsuite:message>
				</netsuite:get-list>
					<error-handler >
						<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="c056d5c1-a0f2-4586-96d6-3a59b2623402" type="ANY">
							<ee:transform doc:name="Set payload" doc:id="983fedce-4ef5-47da-8d02-07359ee9a09c" >
								<ee:message >
									<ee:set-payload ><![CDATA[output application/json --- error.'errorMessage'.'payload']]></ee:set-payload>
								</ee:message>
								<ee:variables >
								</ee:variables>
							</ee:transform>
						</on-error-continue>
					</error-handler>
				</try>
			</route>
			<route>
				<try doc:name="Try" doc:id="6c249f06-c32a-4786-a7a8-e245cde416be" >
					<netsuite:get-list doc:name="Get BillingScheduleDetails" doc:id="5b1edf47-7cd6-475b-b267-52b60ce54741" config-ref="NetSuite_Config">
					<netsuite:message><![CDATA[#[vars.vRetrieveBillingScheduleDetailsBody]]]></netsuite:message>
				</netsuite:get-list>
					<error-handler >
						<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="444e7bd7-08f2-4a16-93bb-241677075a27" type="ANY">
							<ee:transform doc:name="Set payload" doc:id="cb900874-1cfe-490c-abbc-d44905c2fb2b" >
								<ee:message >
									<ee:set-payload ><![CDATA[output application/json --- error.'errorMessage'.'payload']]></ee:set-payload>
								</ee:message>
								<ee:variables >
								</ee:variables>
							</ee:transform>
						</on-error-continue>
					</error-handler>
				</try>
			</route>
		</scatter-gather>
		<try doc:name="Try" doc:id="9075c7fe-109e-49d6-bbb5-d0907a7333b8" >
			<set-variable value="#[%dw 2.0&#10;import * from dw::core::Arrays&#10;import * from dw::core::Strings&#10;&#10;var order = vars.vSGFetchResponse.'0'.payload&#10;var billingSchedules = vars.vSGFetchResponse.'1'.payload&#10;var billingScheduleDetails = vars.vSGFetchResponse.'2'.payload&#10;&#10;var errorType = (&#10;	if(&#10;		(isEmpty(order.getResponse.readResponse)) or&#10;		(isEmpty(billingSchedules.getListResponse.readResponseList.*readResponse)) or &#10;		(isEmpty(billingScheduleDetails.getListResponse.readResponseList.*readResponse))&#10;	) &quot;SYSTEM&quot;&#10;	else if(!(&#10;		(vars.vSGFetchResponse.'0'.payload.getResponse.readResponse.status.@isSuccess ~= &quot;true&quot;) and &#10;		((vars.vSGFetchResponse.'1'.payload.getListResponse.readResponseList.*readResponse.status.@isSuccess default []) every (($) ~= &quot;true&quot;)) and &#10;		((vars.vSGFetchResponse.'2'.payload.getListResponse.readResponseList.*readResponse.status.@isSuccess default []) every (($) ~= &quot;true&quot;))&#10;	)) &quot;DATA&quot;&#10;	else (&#10;		null&#10;	)&#10;)&#10;&#10;output application/json&#10;---&#10;errorType]" doc:name="vErrorFlag" doc:id="d7cc1265-6982-4ca8-9f22-182ea50614fd" variableName="vErrorFlag"/>
			<validation:is-true doc:name="Records retrieved?" doc:id="97c8c16e-7f41-4722-8d29-047e5feb7165" expression="#[isEmpty(vars.vErrorFlag)]" >
				<error-mapping sourceType="VALIDATION:INVALID_BOOLEAN" targetType="CUSTOM:RECORD_RETRIEVAL_ERROR" />
			</validation:is-true>
			<ee:transform doc:name="Set payload, httpStatus" doc:id="be1397be-d9c6-47fd-a1ff-0f95b0889302">
			<ee:message>
				<ee:set-payload resource="dwl/read/readNetsuiteOrderResponseMapping.dwl" />
			</ee:message>
			<ee:variables>
				<ee:set-variable variableName="httpStatus"><![CDATA[200]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
			<error-handler >
				<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="d4a8ef07-1c78-44b7-a9ad-4f6410c0139d" >
					<ee:transform doc:name="Set payload, httpStatus" doc:id="ce788ee3-03f0-49c6-99df-b08fc5999284" >
						<ee:message >
							<ee:set-payload ><![CDATA[%dw 2.0
import * from dw::core::Arrays
import * from dw::core::Strings

var order = vars.vSGFetchResponse.'0'.payload
var billingSchedules = vars.vSGFetchResponse.'1'.payload
var billingScheduleDetails = vars.vSGFetchResponse.'2'.payload

var errorType = (
	if(
		(isEmpty(order.getResponse.readResponse)) or
		(isEmpty(billingSchedules.getListResponse.readResponseList.*readResponse)) or 
		(isEmpty(billingScheduleDetails.getListResponse.readResponseList.*readResponse))
	) "SYSTEM"
	else (
		"DATA"
	)
)

output application/json
---
if(errorType == "DATA") {
	"code": 200,
	"status": "SUCCESS",
    "transactionId": vars.vTransactionId,
    "response": {
    	"message": "RECORD_COULD_NOT_BE_RETIEVED",
    	"details": (
	      	if(!isEmpty(order.getResponse.readResponse)) "Error occurred while retrieving Order details: " ++ (order.getResponse.readResponse.status.statusDetail.message default "SOME_INTERNAL_ERROR_OCCURRED")
	      	else if(!isEmpty(billingSchedules.getListResponse.readResponseList.*readResponse)) "Error occurred while retrieving BillingScheduleDetails: " ++ ((billingSchedules.getListResponse.readResponseList.*readResponse.status filter(($).@isSuccess ~= "false") default [])[0].statusDetail.message default "SOME_INTERNAL_ERROR_OCCURRED")
	      	else "Error occurred while retrieving billingScheduleDetails details: " ++ ((billingScheduleDetails.getListResponse.readResponseList.*readResponse.status filter(($).@isSuccess ~= "false") default [])[0].statusDetail.message default "SOME_INTERNAL_ERROR_OCCURRED")
	    )
    }
}
else {
    "code": 200,
    "status": "SUCCESS",
    "transactionId": vars.vTransactionId,
    "response": {
      "message": "RECORD_COULD_NOT_BE_RETIEVED",
      "details": (
      	if(!isEmpty(order.errorDetail)) (order.description default "") ++ " | " ++ (order.errorDetail default "")
      	else if(!isEmpty(billingSchedules.errorDetail)) (billingSchedules.description default "") ++ " | " ++ (billingSchedules.errorDetail default "")
      	else (billingScheduleDetails.description default "") ++ " | " ++ (billingScheduleDetails.errorDetail default "")
	  )
    }
}]]></ee:set-payload>
						</ee:message>
						<ee:variables >
							<ee:set-variable variableName="httpStatus" ><![CDATA[200]]></ee:set-variable>
						</ee:variables>
					</ee:transform>
				</on-error-continue>
			</error-handler>
		</try>
		<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Response Payload" doc:id="bbffb0cc-d77a-4a8e-8c49-f0b78113d72b" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-get-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendResponse": payload,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response" doc:id="c60cf472-ad5c-4ff7-9476-b4c95fafbb33" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-get-netsuite-order",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit" doc:id="17050764-15ce-4760-b55e-6ba248ce26ac" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-get-netsuite-order",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
	</flow>
</mule>
