<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:validation="http://www.mulesoft.org/schema/mule/validation" xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:netsuite="http://www.mulesoft.org/schema/mule/netsuite" xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/netsuite http://www.mulesoft.org/schema/mule/netsuite/current/mule-netsuite.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/validation http://www.mulesoft.org/schema/mule/validation/current/mule-validation.xsd">
	<flow name="pf-update-netsuite-order" doc:id="b1139fbf-46f7-4491-821d-8077c2f5219b" >
		
		<logger level="INFO" doc:name="LOG INFO: Log Entry" doc:id="993a5afd-160f-4f3f-9e7f-18c67c59f424" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-update-netsuite-order", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Inbound Request" doc:id="ddc864a2-7c9e-4d13-b46e-b054ee0cdadc" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Inbound Request",&#10;	"FlowName" : "pf-update-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="DEBUG" doc:name="LOG DEBUG: Log Inbound Request Payload" doc:id="0deb7bfa-056f-4f67-a10c-777ad54953b8" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Inbound Request Payload",&#10;	"FlowName" : "pf-update-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendRequest": payload,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<ee:transform doc:name="Set vInternalId, vOrderItems, vBillingSchedules" doc:id="2e379ecc-0951-4389-8e4b-8c964789138e" >
			<ee:message />
			<ee:variables >
				<ee:set-variable variableName="vInternalId" ><![CDATA[%dw 2.0
output application/json
---
attributes.queryParams.'orderNSId' as Number as String]]></ee:set-variable>
				<ee:set-variable variableName="vOrderItems" ><![CDATA[%dw 2.0
output application/json
---
payload.order.orderItems default {}]]></ee:set-variable>
				<ee:set-variable variableName="vBillingSchedules" ><![CDATA[%dw 2.0
output application/json
---
payload.order.billingSchedules default {}]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<try doc:name="Try" doc:id="d2ca321e-a748-4697-b65e-0e509d477145" >
			<set-variable value='#["0" as Number]' doc:name="vRetryCount" doc:id="0a6bcbd7-c9f5-4675-b78c-baae74379c15" variableName="vRetryCount" />
			<until-successful maxRetries="${netsuite.retry.maxRetries}" doc:name="Until Successful" doc:id="0e6fbb9e-bf9f-45fb-87b0-6a621bdde602" millisBetweenRetries="${netsuite.retry.timePeriod}" >
				<try doc:name="Try" doc:id="ee8dd7e2-d078-4a0c-9030-fcd6d71dd9e8" >
					
					<scatter-gather doc:name="Create Update BillingScheduleDetails" doc:id="504f6a11-9c82-422d-b24e-51f65c5a35a6" target="vSGBillingScheduleDetailsResponse">
						<route >
							<choice doc:name="New BillingScheduleDetails ?" doc:id="ec4b62dd-7981-4c6e-b5b6-0c2a7dad34d6" >
								<when expression="#[!isEmpty(flatten(flatten(payload.order.orderItems.billingSchedule).billingScheduleDetails) distinctBy(($).'name') filter(($).operation ~= &quot;CREATE&quot;))]">
									<ee:transform doc:name="Set vCreateBillingScheduleDetailsBody" doc:id="1fbfe06e-7bcb-4320-ac71-29f99729b99b">
								<ee:message />
								<ee:variables>
											<ee:set-variable resource="dwl/update/createNetsuiteBillingScheduleDetailsMapping.dwl" variableName="vCreateBillingScheduleDetailsBody" />
								</ee:variables>
							</ee:transform>
									<logger level="DEBUG" doc:name="LOG DEBUG: Before BillingScheduleDetail create" doc:id="3703df38-26af-4f67-b8f4-241ca04e6db8" message='#[%dw 2.0&#10;output application/json writeAttributes=true&#10;---&#10;{ 	&#10;	"FlowName" : "pf-update-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendRequest": vars.vCreateBillingScheduleDetailsBody,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
									<netsuite:add-list doc:name="Add BillingScheduleDetails" doc:id="06fdc935-d1fa-417b-842e-6580a94d25a1" config-ref="NetSuite_Config" type="#[p('netsuite.custRecord.billingScheduleDetails.type')]">
								<netsuite:message><![CDATA[#[vars.vCreateBillingScheduleDetailsBody]]]></netsuite:message>
							</netsuite:add-list>
									<logger level="DEBUG" doc:name="LOG DEBUG: After BillingScheduleDetail create" doc:id="9bea2db6-c7c0-46f5-8cfb-56630df87494" message='#[%dw 2.0&#10;output application/json writeAttributes=true&#10;---&#10;{ 	&#10;	"Message" : "After BillingScheduleDetail create",&#10;	"FlowName" : "pf-update-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendRequest": payload,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
									<try doc:name="Try" doc:id="ff78650f-b84d-4a00-a2f5-18eae56e886c" >
										<validation:is-true doc:name="Are BillingScheduleDetails created?" doc:id="ef070605-7fd7-4ce8-baaa-c01762b21db2" expression='#[%dw 2.0 import * from dw::core::Arrays output application/json --- (payload.addListResponse.writeResponseList.*writeResponse.status.@isSuccess default []) every (($) ~= "true")]' message='#["Error occurred while creating BillingScheduleDetails in Netsuite"]' />
										<error-handler >
											<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="7617e85c-0756-492b-9617-515279d1da3b" >
												<set-variable value='#[%dw 2.0&#10;import * from dw::core::Arrays&#10;output application/json&#10;var netsuiteError = (payload.addListResponse.writeResponseList.*writeResponse.status filter(($).@isSuccess ~= "false") default [])[0].statusDetail.message&#10;---&#10;{&#10;	"message": "Error occurred while creating BillingSchedules",&#10;	"details": netsuiteError&#10;}]' doc:name="vError" doc:id="8b8771a4-d7a0-4fb4-aaa7-48023f748452" variableName="vError" />
											</on-error-continue>
										</error-handler>
									</try>
								</when>
								<otherwise >
									<logger level="INFO" doc:name="LOG INFO: No new BillingScheduleDetail created" doc:id="8e384cc1-256e-410c-9e6b-5bff79233b5b" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "No new BillingScheduleDetail is passed from Salesforce for " ++ (vars.vInternalId default "-1") ++ " Order.",&#10;	"FlowName" : "pf-update-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
									<set-payload value="#[{}]" doc:name="Set Payload" doc:id="0cebf852-a951-4497-9750-a5b482dd76f6" />
								</otherwise>
							</choice>
						</route>
						<route >
							<choice doc:name="Updated BillingSchedules ?" doc:id="d0493802-1352-4c7e-a0fe-13e669289c05" >
								<when expression="#[!isEmpty(flatten(flatten(payload.order.orderItems.billingSchedule).billingScheduleDetails) distinctBy(($).'name') filter(($).operation ~= &quot;UPDATE&quot;))]">
									<ee:transform doc:name="Set vUpdateBillingScheduleDetailsBody" doc:id="5e9aebd2-ead6-4dae-8eb0-fa456c04e843">
								<ee:message>
								</ee:message>
								<ee:variables>
											<ee:set-variable resource="dwl/update/updateNetsuiteBillingScheduleDetailsMapping.dwl" variableName="vUpdateBillingScheduleDetailsBody" />
								</ee:variables>
							</ee:transform>
									<logger level="DEBUG" doc:name="LOG DEBUG: Before BillingScheduleDetail update" doc:id="26d6890b-5af8-4e68-986e-24c46e08f378" message='#[%dw 2.0&#10;output application/json writeAttributes=true&#10;---&#10;{ 	&#10;	"Message" : "Before BillingScheduleDetail update",&#10;	"FlowName" : "pf-update-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendRequest": vars.vUpdateBillingScheduleDetailsBody,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]'/>
									<netsuite:update-list doc:name="Update BillingScheduleDetails" doc:id="b6a30726-dbe7-4f54-a115-c64d49a2c9d7" config-ref="NetSuite_Config" type="#[p('netsuite.custRecord.billingScheduleDetails.type')]">
								<netsuite:message><![CDATA[#[vars.vUpdateBillingScheduleDetailsBody]]]></netsuite:message>
							</netsuite:update-list>
									<logger level="DEBUG" doc:name="LOG DEBUG: After BillingScheduleDetail update" doc:id="1ae2ba2d-f2a1-4cf0-a73f-836877a8724b" message='#[%dw 2.0&#10;output application/json writeAttributes=true&#10;---&#10;{ 	&#10;	"Message" : "After BillingScheduleDetail update",&#10;	"FlowName" : "pf-update-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendRequest": payload,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
									<try doc:name="Try" doc:id="26d0ba0e-09c5-46d8-a105-85ac35c598fd" >
										<validation:is-true doc:name="Are BillingScheduleDetails updated?" doc:id="8940eff2-c04d-447a-9aa2-7012da3006d2" expression='#[%dw 2.0 import * from dw::core::Arrays output application/json --- (payload.updateListResponse.writeResponseList.*writeResponse.status.@isSuccess default []) every (($) ~= "true")]' message='#["Error occurred while updating BillingScheduleDetails in Netsuite"]' />
										<error-handler >
											<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="a5000c6b-68c2-4d8b-87bb-9776ab055113" >
												<set-variable value='#[%dw 2.0&#10;import * from dw::core::Arrays&#10;output application/json&#10;var netsuiteError = (payload.updateListResponse.writeResponseList.*writeResponse.status filter(($).@isSuccess ~= "false") default [])[0].statusDetail.message&#10;---&#10;{&#10;	"message": "Error occurred while updating BillingSchedules",&#10;	"details": netsuiteError&#10;}]' doc:name="vError" doc:id="747b6f86-b44b-47c0-bb9b-829a527b858f" variableName="vError" />
											</on-error-continue>
										</error-handler>
									</try>
								</when>
								<otherwise>
									<logger level="INFO" doc:name="LOG INFO: No updated BillingScheduleDetail sent" doc:id="a9152f11-66ac-4151-aa22-a01346b606ab" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "No updated BillingScheduleDetail is passed from Salesforce for " ++ (vars.vInternalId default "-1") ++ " Order.",&#10;	"FlowName" : "pf-update-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
									<set-payload value="#[{}]" doc:name="Set Payload" doc:id="7a3bf02e-7311-498c-8f58-a446d7b5c8a4" />
								</otherwise>
							</choice>
						</route>
					</scatter-gather>
					
					
					<set-variable value="#[output application/json&#10;var netsuiteCreatedBSDetailIds = vars.vSGBillingScheduleDetailsResponse.'0'.payload.addListResponse.writeResponseList.*writeResponse default []&#10;var salesforceCreatedBSDetailIds = (flatten(payload.order.orderItems.billingSchedule.billingScheduleDates).billingScheduleDetail).sfId default []&#10;---&#10;{(salesforceCreatedBSDetailIds map() -&gt; (&#10;	($) mapObject(v, k, i) -&gt; {&#10;		(k as String): netsuiteCreatedBSDetailIds[$].baseRef.@internalId&#10;	}&#10;))}]" doc:name="vSFNSMapping" doc:id="543d3af1-f2fd-4cba-8c56-c4fe834a46ad" variableName="vSFNSMapping"/>
					<scatter-gather doc:name="Create Update BillingSchedules" doc:id="cab735e9-5ea5-46e6-ae13-c6dc450445d0" target="vSGBillingSchedulesResponse">
						<route >
							<choice doc:name="New BillingSchedules ?" doc:id="c1acd2d6-dbdb-4059-a24e-a36f4f9fbd80" >
								<when expression='#[!isEmpty(payload.order.orderItems.billingSchedule filter(($).operation ~= "CREATE"))]'>
									<ee:transform doc:name="Set vCreateBillingSchedulesBody" doc:id="2b63368a-0c11-4733-977c-78feca4d3e07">
								<ee:message />
								<ee:variables>
											<ee:set-variable resource="dwl/update/createNetsuiteBillingSchedulesMapping.dwl" variableName="vCreateBillingSchedulesBody" />
								</ee:variables>
							</ee:transform>
									<logger level="DEBUG" doc:name="LOG DEBUG: Before BillingSchedule create" doc:id="4379ccc4-9517-4534-a6bd-06d4624cb17f" message='#[%dw 2.0&#10;output application/json writeAttributes=true&#10;---&#10;{ 	&#10;	"BusinessKey" : vars.vBusinessKey,&#10;  	"FlowName" : "pf-update-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendRequest": vars.vCreateBillingSchedulesBody,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
									<netsuite:add-list doc:name="Add BillingSchedules" doc:id="592b1bca-acef-4177-b45e-b003ee141e49" config-ref="NetSuite_Config" type="billingSchedule">
								<netsuite:message><![CDATA[#[vars.vCreateBillingSchedulesBody]]]></netsuite:message>
							</netsuite:add-list>
									<logger level="DEBUG" doc:name="LOG DEBUG: After BillingSchedule create" doc:id="2cb1f066-1357-4f2a-916b-45d16cbf863a" message='#[%dw 2.0&#10;output application/json writeAttributes=true&#10;---&#10;{ 	&#10;	"Message" : "After BillingSchedule create",&#10;	"FlowName" : "pf-update-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendRequest": payload,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
									<try doc:name="Try" doc:id="aaea8cd3-9866-4408-82bd-dab29adb1033" >
										<validation:is-true doc:name="Are BillingSchedules created?" doc:id="7cc9c045-d1b2-44da-b046-41fe14b09dca" expression='#[%dw 2.0 import * from dw::core::Arrays output application/json --- (payload.addListResponse.writeResponseList.*writeResponse.status.@isSuccess default []) every (($) ~= "true")]' message='#["Error occurred while creating BillingSchedules in Netsuite"]' />
										<error-handler >
											<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="662ef86b-9d39-40a2-aad8-f98b22a8a9f3" >
												<set-variable value='#[%dw 2.0&#10;import * from dw::core::Arrays&#10;output application/json&#10;var netsuiteError = (payload.addListResponse.writeResponseList.*writeResponse.status filter(($).@isSuccess ~= "false") default [])[0].statusDetail.message&#10;---&#10;{&#10;	"message": "Error occurred while creating BillingSchedules",&#10;	"details": netsuiteError&#10;}]' doc:name="vError" doc:id="3d112d41-5284-4cdf-9d87-b6ed4bf66467" variableName="vError" />
											</on-error-continue>
										</error-handler>
									</try>
								</when>
								<otherwise >
									<logger level="INFO" doc:name="LOG INFO: No new BillingSchedule created" doc:id="8d1d36b8-239f-4aa5-ba4d-1a4db762b71c" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "No new BillingSchedule is passed from Salesforce for " ++ (vars.vInternalId default "-1") ++ " Order.",&#10;	"FlowName" : "pf-update-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
									<set-payload value="#[{}]" doc:name="Set Payload" doc:id="066dfff8-9758-4e2a-902d-e605f08d7873" />
								</otherwise>
							</choice>
						</route>
						<route >
							<choice doc:name="Updated BillingSchedules ?" doc:id="527cd32c-e31e-45d9-b5e9-e39af5612d6f" >
								<when expression='#[!isEmpty(payload.order.orderItems.billingSchedule filter(($).operation ~= "UPDATE"))]'>
									<ee:transform doc:name="Set vUpdateBillingSchedulesBody" doc:id="d856db8a-073d-4d06-b040-fa4a04acd2db">
								<ee:message>
								</ee:message>
								<ee:variables>
											<ee:set-variable resource="dwl/update/updateNetsuiteBillingSchedulesMapping.dwl" variableName="vUpdateBillingSchedulesBody" />
								</ee:variables>
							</ee:transform>
									<logger level="DEBUG" doc:name="LOG DEBUG: Before BillingSchedule update" doc:id="19a330be-38bb-4441-8fc8-54ad70817150" message='#[%dw 2.0&#10;output application/json writeAttributes=true&#10;---&#10;{ 	&#10;	"Message" : "Before BillingSchedule update",&#10;	"FlowName" : "pf-update-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendRequest": vars.vUpdateBillingSchedulesBody,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]'/>
									<netsuite:update-list doc:name="Update BillingSchedules" doc:id="45eda513-67f5-4d3e-91c0-008c496f8a1b" config-ref="NetSuite_Config" type="billingSchedule">
								<netsuite:message><![CDATA[#[vars.vUpdateBillingSchedulesBody]]]></netsuite:message>
							</netsuite:update-list>
									<logger level="DEBUG" doc:name="LOG DEBUG: After BillingSchedule update" doc:id="ecf8ec8b-b942-474f-9079-67517898d7f3" message='#[%dw 2.0&#10;output application/json writeAttributes=true&#10;---&#10;{ 	&#10;	"Message" : "After BillingSchedule update",&#10;	"FlowName" : "pf-update-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendRequest": payload,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
									<try doc:name="Try" doc:id="453c5238-6df4-4f54-9858-bce08d72ffea" >
										<validation:is-true doc:name="Are BillingSchedules updated?" doc:id="6f87548c-f8d5-4a28-b913-aeb2264d3144" expression='#[%dw 2.0 import * from dw::core::Arrays output application/json --- (payload.updateListResponse.writeResponseList.*writeResponse.status.@isSuccess default []) every (($) ~= "true")]' message='#["Error occurred while updating BillingSchedules in Netsuite"]' />
										<error-handler >
											<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="8c18b9e7-4994-4c63-a0af-553e4138c593" >
												<set-variable value='#[%dw 2.0&#10;import * from dw::core::Arrays&#10;output application/json&#10;var netsuiteError = (payload.updateListResponse.writeResponseList.*writeResponse.status filter(($).@isSuccess ~= "false") default [])[0].statusDetail.message&#10;---&#10;{&#10;	"message": "Error occurred while updating BillingSchedules",&#10;	"details": netsuiteError&#10;}]' doc:name="vError" doc:id="59b2058e-0de5-48ce-891c-3f4e02bc3cc8" variableName="vError" />
											</on-error-continue>
										</error-handler>
									</try>
								</when>
								<otherwise>
									<logger level="INFO" doc:name="LOG INFO: No updated BillingSchedule sent" doc:id="55df5c98-0ddc-40b1-993c-ddf90e20792f" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "No updated BillingSchedule is passed from Salesforce for " ++ (vars.vInternalId default "-1") ++ " Order.",&#10;	"FlowName" : "pf-update-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
									<set-payload value="#[{}]" doc:name="Set Payload" doc:id="f9a5d0e9-2224-468d-8202-e3c64f19fd42" />
								</otherwise>
							</choice>
						</route>
					</scatter-gather>
					<ee:transform doc:name="Set vBSFlag, vError" doc:id="f56a9615-f055-469f-844b-18a8c99e5a2e" >
								<ee:message />
								<ee:variables >
							<ee:set-variable variableName="vError" ><![CDATA[%dw 2.0 
import * from dw::core::Arrays 
var bsCreated = (vars.vSGBillingSchedulesResponse.'0'.payload.addListResponse.writeResponseList.*writeResponse.status.@isSuccess default []) every (($) ~= "true")
var bsUpdated = (vars.vSGBillingSchedulesResponse.'1'.payload.updateListResponse.writeResponseList.*writeResponse.status.@isSuccess default []) every (($) ~= "true")
output application/json
---
if(!(bsCreated and bsUpdated)) (
	if(!bsCreated) {
		"message": "Error occurred while creating BillingSchedules",
		"details": vars.vSGBillingSchedulesResponse.'0'.payload.addListResponse.writeResponseList.*writeResponse[0].status.statusDetail.message
	}
	else {
		"message": "Error occurred while updating BillingSchedules",
		"details": vars.vSGBillingSchedulesResponse.'1'.payload.updateListResponse.writeResponseList.*writeResponse[0].status.statusDetail.message
	}
)
else {
	
}]]></ee:set-variable>
							<ee:set-variable variableName="vBSFlag" ><![CDATA[%dw 2.0 
import * from dw::core::Arrays 
var bsCreated = (vars.vSGBillingSchedulesResponse.'0'.payload.addListResponse.writeResponseList.*writeResponse.status.@isSuccess default []) every (($) ~= "true")
var bsUpdated = (vars.vSGBillingSchedulesResponse.'1'.payload.updateListResponse.writeResponseList.*writeResponse.status.@isSuccess default []) every (($) ~= "true")
output application/json
---
(bsCreated and bsUpdated)]]></ee:set-variable>
								</ee:variables>
							</ee:transform>
					<validation:is-true doc:name="Are BillingSchedules created/updated successfully?" doc:id="57917056-e9db-49a9-8cf8-754f64e89805" expression="#[vars.vBSFlag]">
						<error-mapping sourceType="VALIDATION:INVALID_BOOLEAN" targetType="CUSTOM:DATA_VALIDATION_ERROR" />
					</validation:is-true>
					<ee:transform doc:name="Set vUpdateOrderBody" doc:id="755eb9e2-74e5-4527-86ad-843d5e02bb05">
			<ee:message />
			<ee:variables>
				<ee:set-variable resource="dwl/update/updateNetsuiteOrderMapping.dwl" variableName="vUpdateOrderBody" />
			</ee:variables>
		</ee:transform>
					<logger level="DEBUG" doc:name="LOG DEBUG: Before SalesOrder update" doc:id="467175aa-3d08-40d7-9941-866d1f5196db" message='#[%dw 2.0&#10;output application/json writeAttributes=true&#10;---&#10;{ 	&#10;	"Message" : "Before SalesOrder update",&#10;	"FlowName" : "pf-update-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendRequest": vars.vUpdateOrderBody,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
					<netsuite:update doc:name="Update SalesOrder" doc:id="9e7b3132-8b23-4d33-b93e-2668d04cc5d7" config-ref="NetSuite_Config" type="salesOrder" target="vUpdateOrderResponse">
						<netsuite:message ><![CDATA[#[vars.vUpdateOrderBody]]]></netsuite:message>
					</netsuite:update>
					<logger level="DEBUG" doc:name="LOG DEBUG: After SalesOrder update" doc:id="0dd9a12f-65d3-4182-8306-753bd3a289f8" message='#[%dw 2.0&#10;output application/json writeAttributes=true&#10;---&#10;{ 	&#10;	"Message" : "After SalesOrder update",&#10;	"FlowName" : "pf-create-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendRequest": vars.vUpdateOrderResponse,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]'/>
					<try doc:name="Try" doc:id="6953df89-f2f9-46d1-bf53-061852572569" >
						<validation:is-true doc:id="2a58bceb-b002-444e-bc91-92139d1ac61a" doc:name="Is SalesOrder updated?" expression='#[output application/json --- vars.vUpdateOrderResponse.updateResponse.writeResponse.status.@isSuccess ~= "true"]' message='#["Error occurred while updating SalesOrder in Netsuite"]' />
						<error-handler >
							<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="5f734518-06fe-4e5c-ade6-c96036998c4c" >
								<set-variable value='#[%dw 2.0&#10;import * from dw::core::Arrays&#10;output application/json&#10;var netsuiteError = (vars.vUpdateOrderResponse.updateResponse.writeResponse.status.statusDetail.message)&#10;---&#10;{&#10;	"message": "Error occurred while updating SalesOrder",&#10;	"details": netsuiteError&#10;}]' doc:name="vError" doc:id="7c68e87c-3672-4051-90aa-022ce1569c64" variableName="vError" />
								<raise-error doc:name="CUSTOM:DATA_VALIDATION_ERROR" doc:id="71863145-de6e-47f7-a2cd-53420e7e8ce4" type="CUSTOM:DATA_VALIDATION_ERROR" />
							</on-error-propagate>
						</error-handler>
					</try>
					
					<choice doc:name="Deleted BillingScheduleDetails ?" doc:id="d3da0db8-0abe-48f4-a8ec-d25340340e90">
								<when expression="#[!isEmpty(flatten(flatten(payload.order.orderItems.billingSchedule).billingScheduleDetails) distinctBy(($).'name') filter(($).operation ~= &quot;DELETE&quot;))]">
									<ee:transform doc:name="Set vDeleteBillingScheduleDetailsBody" doc:id="58d55c43-f8bb-47df-8e28-645386a9741d">
								<ee:message>
								</ee:message>
								<ee:variables>
									<ee:set-variable resource="dwl/update/deleteNetsuiteBillingScheduleDetailsMapping.dwl" variableName="vDeleteBillingScheduleDetailsBody" />
								</ee:variables>
							</ee:transform>
									<netsuite:delete-list doc:name="Delete BillingSchedules" doc:id="10e9224c-d9ec-4d67-bef6-7a2490d8b0a5" config-ref="NetSuite_Config" type="billingSchedule" refType="RecordRef" target="vDeleteBillingSchedulesResponse">
								<netsuite:message><![CDATA[#[vars.vDeleteBillingSchedulesBody]]]></netsuite:message>
							</netsuite:delete-list>
									<logger level="INFO" doc:name="LOG INFO: BillingSchedules deleted" doc:id="4c2273b3-0b90-4c62-8e59-8c8f7f4d59a0" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : (sizeOf(payload.order.orderItems.billingSchedule filter(($).operation ~= "DELETE")) default "-1") ++ " BillingSchedules deleted", &#10;	"FlowName" : "pf-update-netsuite-order", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
							<try doc:name="Try" doc:id="2aee81f2-aa0e-4090-957c-e6926088ac27" >
								<validation:is-true doc:name="Are BillingSchedules deleted?" doc:id="96213281-897a-4746-9548-b5cda142893e" expression='#[%dw 2.0 import * from dw::core::Arrays output application/json --- (vars.vDeleteBillingSchedulesResponse.deleteListResponse.writeResponseList.*writeResponse.status.@isSuccess default []) every (($) ~= "true")]' message='#["Error occurred while deleting BillingSchedules from Netsuite"]' />
								<error-handler >
									<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="6eee3d1a-416b-4e03-b142-82be6852efec" >
										<set-variable value='#[%dw 2.0&#10;import * from dw::core::Arrays&#10;output application/json&#10;var netsuiteError = (vars.vDeleteBillingSchedulesResponse.deleteListResponse.writeResponseList.*writeResponse.status filter(($).@isSuccess ~= "false") default [])[0].statusDetail.message&#10;---&#10;{&#10;	"message": "Error occurred while deleting BillingSchedules",&#10;	"details": netsuiteError&#10;}]' doc:name="vError" doc:id="8e4492f6-a337-4a90-9549-1fa010318ba5" variableName="vError" />
										<raise-error doc:name="CUSTOM:DATA_VALIDATION_ERROR" doc:id="53fb55d4-0e70-424a-beab-9589590f172c" type="CUSTOM:DATA_VALIDATION_ERROR" />
									</on-error-propagate>
								</error-handler>
							</try>
								</when>
								<otherwise>
									<logger level="INFO" doc:name="LOG INFO: No deleted BillingScheduleDetail sent" doc:id="4472a777-0d58-4d08-a044-0b8a87f13cd4" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "No deleted BillingScheduleDetail is passed from Salesforce for " ++ (vars.vInternalId default "-1") ++ " Order.",&#10;	"FlowName" : "pf-update-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
								</otherwise>
							</choice>
					
					
					<choice doc:name="Deleted BillingSchedules ?" doc:id="a1840c12-fe07-46d2-ac8a-4856919f8205">
								<when expression='#[!isEmpty(payload.order.orderItems.billingSchedule filter(($).operation ~= "DELETE"))]'>
									<ee:transform doc:name="Set vDeleteBillingSchedulesBody" doc:id="cfc8b579-98b9-4450-b010-b0ea59b39423">
								<ee:message>
								</ee:message>
								<ee:variables>
											<ee:set-variable resource="dwl/update/deleteNetsuiteBillingSchedulesMapping.dwl" variableName="vDeleteBillingSchedulesBody" />
								</ee:variables>
							</ee:transform>
									<netsuite:delete-list doc:name="Delete BillingSchedules" doc:id="e4826735-5b99-46b8-9bb9-2c247fe7a41e" config-ref="NetSuite_Config" type="billingSchedule" refType="RecordRef" target="vDeleteBillingSchedulesResponse">
								<netsuite:message><![CDATA[#[vars.vDeleteBillingSchedulesBody]]]></netsuite:message>
							</netsuite:delete-list>
									<logger level="INFO" doc:name="LOG INFO: BillingSchedules deleted" doc:id="d7f0d34e-bc0c-496d-a4e6-8387cd86dbec" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : (sizeOf(payload.order.orderItems.billingSchedule filter(($).operation ~= "DELETE")) default "-1") ++ " BillingSchedules deleted", &#10;	"FlowName" : "pf-update-netsuite-order", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
							<try doc:name="Try" doc:id="36533988-bc27-44d5-ad28-21b6074e35c9" >
								<validation:is-true doc:name="Are BillingSchedules deleted?" doc:id="3671b56e-8751-4076-8eeb-edf710f86d1c" expression='#[%dw 2.0 import * from dw::core::Arrays output application/json --- (vars.vDeleteBillingSchedulesResponse.deleteListResponse.writeResponseList.*writeResponse.status.@isSuccess default []) every (($) ~= "true")]' message='#["Error occurred while deleting BillingSchedules from Netsuite"]' />
								<error-handler >
									<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="b9b58bff-eb1a-4ddb-a8ed-9a548562c728" >
										<set-variable value='#[%dw 2.0&#10;import * from dw::core::Arrays&#10;output application/json&#10;var netsuiteError = (vars.vDeleteBillingSchedulesResponse.deleteListResponse.writeResponseList.*writeResponse.status filter(($).@isSuccess ~= "false") default [])[0].statusDetail.message&#10;---&#10;{&#10;	"message": "Error occurred while deleting BillingSchedules",&#10;	"details": netsuiteError&#10;}]' doc:name="vError" doc:id="ab7ddf00-2dba-4fcb-a35a-beffe4a70c9c" variableName="vError" />
										<raise-error doc:name="CUSTOM:DATA_VALIDATION_ERROR" doc:id="5d72bbfb-9816-4d02-af92-6f1261470d05" type="CUSTOM:DATA_VALIDATION_ERROR" />
									</on-error-propagate>
								</error-handler>
							</try>
								</when>
								<otherwise>
									<logger level="INFO" doc:name="LOG INFO: No deleted BillingSchedule sent" doc:id="46420ea8-6278-4f5c-9f1d-444e28ff73c7" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "No deleted BillingSchedule is passed from Salesforce for " ++ (vars.vInternalId default "-1") ++ " Order.",&#10;	"FlowName" : "pf-update-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
								</otherwise>
							</choice>
					<ee:transform doc:name="Set vRetrieveOrderBody, vRetrieveBillingSchedulesBody, vRetrieveBillingScheduleDetailsBody" doc:id="2ab652b3-e097-4c67-8e86-e5c2cea3c30b" >
						<ee:message />
						<ee:variables >
							<ee:set-variable variableName="vRetrieveBillingSchedulesBody" ><![CDATA[%dw 2.0
output application/xml
ns ns0 urn:messages_2021_1.platform.webservices.netsuite.com
ns ns01 urn:common_2021_1.platform.webservices.netsuite.com
ns ns02 urn:core_2021_1.platform.webservices.netsuite.com
ns ns03 urn:relationships_2021_1.platform.webservices.netsuite.com
ns ns04 urn:sales.transactions.webservices.netsuite.com
ns xsi http://www.w3.org/2001/XMLSchema-instance
---
{
	ns0#getList: ((vars.vSGBillingSchedulesResponse.'0'.payload.addListResponse.writeResponseList.*writeResponse.baseRef.@internalId default []) map() -> {
		ns01#baseRef @("xmlns:ns0": ns0, "xmlns:ns02": ns02, xsi#"type": "ns02:RecordRef", "internalId": ($), "type": "billingSchedule"): ""
	}) reduce ($$ ++ $)
}]]></ee:set-variable>
							<ee:set-variable variableName="vRetrieveOrderBody" ><![CDATA[%dw 2.0
output application/xml
ns ns0 urn:messages_2021_1.platform.webservices.netsuite.com
ns ns01 urn:common_2021_1.platform.webservices.netsuite.com
ns ns02 urn:core_2021_1.platform.webservices.netsuite.com
ns ns03 urn:relationships_2021_1.platform.webservices.netsuite.com
ns ns04 urn:sales.transactions.webservices.netsuite.com
ns xsi http://www.w3.org/2001/XMLSchema-instance
---
{
	ns0#get: {
		ns0#baseRef @("xmlns:ns0": ns0, "xmlns:ns02": ns02, xsi#"type": "ns02:RecordRef", "internalId": vars.vUpdateOrderResponse.updateResponse.writeResponse.baseRef.@internalId, "type": "salesOrder"): ""
	}
}]]></ee:set-variable>
							<ee:set-variable variableName="vRetrieveBillingScheduleDetailsBody" ><![CDATA[%dw 2.0
output application/xml
ns ns0 urn:messages.platform.webservices.netsuite.com
ns ns01 urn:core.platform.webservices.netsuite.com
ns xsi http://www.w3.org/2001/XMLSchema-instance
---
{
	ns0#getList: ((vars.vSGBillingScheduleDetailsResponse.'0'.payload.addListResponse.writeResponseList.*writeResponse.baseRef.@internalId default []) map() -> {
		ns01#baseRef @(internalId: ($) , "typeId": p('netsuite.custRecord.billingScheduleDetails.internalId'), xsi#"type": "ns01:CustomRecordRef"): null
	}) reduce ($$ ++ $)
}]]></ee:set-variable>
						</ee:variables>
					</ee:transform>
					<scatter-gather doc:name="Fetch records" doc:id="28ac26ae-56fb-4c24-b5e1-3fe9d6068054" target="vSGFetchResponse" >
						<route >
							<netsuite:get doc:name="Get SalesOrder" doc:id="b32f9b16-4324-4d72-a8c9-b0978419fbc8" config-ref="NetSuite_Config" refType="RecordRef" type="salesOrder" >
								<netsuite:message ><![CDATA[#[vars.vRetrieveOrderBody]]]></netsuite:message>
							</netsuite:get>
						</route>
						<route >
							<choice doc:name="Is BillingSchedule required" doc:id="52c09715-0dce-493d-848d-c827741feab0" >
								<when expression='#[!isEmpty(payload.order.orderItems.billingSchedule filter(($).operation ~= "CREATE"))]' >
									<netsuite:get-list doc:name="Get BillingSchedules" doc:id="61814cc5-f0ea-4e31-9946-140a9cab9558" config-ref="NetSuite_Config" refType="RecordRef" type="billingSchedule" >
										<netsuite:message ><![CDATA[#[vars.vRetrieveBillingSchedulesBody]]]></netsuite:message>
									</netsuite:get-list>
								</when>
							</choice>
						</route>
						<route >
							<choice doc:name="Is BillingScheduleDetail required" doc:id="7dc1b02f-aaf4-4e02-a152-164245d79da1">
								<when expression='#[!isEmpty(flatten(flatten(payload.order.orderItems.billingSchedule).billingScheduleDetails) filter(($).operation ~= "CREATE"))]'>
									<netsuite:get-list doc:name="Get BillingScheduleDetails" doc:id="f0ae0b95-5595-4d75-9a6b-d0e3b9e669b7" config-ref="NetSuite_Config">
										<netsuite:message ><![CDATA[#[vars.vRetrieveBillingScheduleDetailsBody]]]></netsuite:message>
									</netsuite:get-list>
								</when>
							</choice>
						</route>
					</scatter-gather>
					<error-handler >
						<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="92781213-8ad5-409e-a049-cd0b9c55a9de" type="NETSUITE:CONNECTIVITY, NETSUITE:SESSION_TIMED_OUT" >
							<set-variable value="#[(vars.vRetryCount as Number) + 1]" doc:name="Update vRetryCount" doc:id="46baf2e0-9804-4bce-802d-353fd46a764c" variableName="vRetryCount" />
						</on-error-propagate>
						<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="b9dbc0c4-631b-4b64-8f3c-97518e41b56c" type="NETSUITE:INVALID_VERSION, NETSUITE:NETSUITE_ERROR, NETSUITE:NETSUITE_SOAP_FAULT, NETSUITE:USER_ERROR, CUSTOM:DATA_VALIDATION_ERROR" >
							<ee:transform doc:name="Set vUpdateFlag, vErrorBody" doc:id="a31fa6e9-2cb8-41a3-9438-4cedca056769" >
								<ee:message />
								<ee:variables >
									<ee:set-variable variableName="vUpdateFlag" ><![CDATA[false]]></ee:set-variable>
									<ee:set-variable variableName="vErrorBody" ><![CDATA[%dw 2.0
output application/json
---
if(!isEmpty(vars.vError)) // custom error
(
	if(typeOf(vars.vError) ~= "Array") // multiple routes failed
	do {
		var err = read((vars.vError[0]), "application/json")
		---
		{
			"message": err.message,
			"details": err.details
		}
	}
	else 
	{
		"message": vars.vError.message,
		"details": vars.vError.details
	}
)
else 
{
	"message": "Error occurred while creating SalesOrder/BillingSchedules",
	"details": error.description	
}]]></ee:set-variable>
								</ee:variables>
							</ee:transform>
						</on-error-continue>
						<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="f22523fa-ed17-4ad0-985e-4fbb1728bd11" type="MULE:COMPOSITE_ROUTING">
							<ee:transform doc:name="Set vUpdateFlag, vErrorBody" doc:id="85ffd662-77a0-4ec8-a199-c4665e6357b5" >
								<ee:message />
								<ee:variables >
									<ee:set-variable variableName="vUpdateFlag" ><![CDATA[false]]></ee:set-variable>
									<ee:set-variable variableName="vErrorBody" ><![CDATA[%dw 2.0
var routingError = (
	if(!isEmpty(error.errorMessage.payload.failures['0'])) (
		error.errorMessage.payload.failures['0']
	)
	else (
		error.errorMessage.payload.failures['1']
	)
)
output application/json
---
{
	"message": routingError.description,
	"details": routingError.detailedDescription
}]]></ee:set-variable>
								</ee:variables>
							</ee:transform>
						</on-error-continue>
					</error-handler>
				</try>
			</until-successful>
			<ee:transform doc:name="Set payload, httpStatus" doc:id="e277c21c-04c2-4478-94bd-b4a59beefaca" >
				<ee:message >
					<ee:set-payload ><![CDATA[%dw 2.0
import * from dw::core::Strings
var orderRecord = vars.vSGFetchResponse.'0'.payload.getResponse.readResponse.record default {}
var billingScheduleRecords = vars.vSGFetchResponse.'1'.payload.getListResponse.readResponseList.*readResponse default []
var billingScheduleDetailRecords = vars.vSGFetchResponse.'2'.payload.getListResponse.readResponseList.*readResponse default []
var inputBSDetails = (flatten(flatten(payload.order.orderItems.billingSchedule).billingScheduleDetails))
output application/json writeAttributes=true
---
if(!isEmpty(orderRecord)) {
	"code": 200,
    "status": "SUCCESS",
    "transactionId": vars.vTransactionId,
	"response": {
        "order": {
			"sfId": orderRecord.customFieldList.StringCustomFieldRef__custbody_salesforce_id.value default null,
			"netsuiteId": orderRecord.@internalId default null,
			"netsuiteOrderNumber": orderRecord.tranId default null,
            
            ("orderItems": [] ++ (
                orderRecord.itemList.*item map(v, i) -> {
                	"sfId": (v).customFieldList.StringCustomFieldRef__custcol_salesforce_id.value default null,
                	"nsId": (v).line default null,
                	
                	"billingSchedule": do {
                		var thisBS = (billingScheduleRecords filter(($).record.@internalId ~= ((v).billingSchedule.@internalId)))[0].record
                		var thisBSSfId = substringAfter((thisBS).name, "Salesforce Id: ")
                		var billingScheduleDates = (
                			thisBS.recurrenceList.*billingScheduleRecurrence map() -> do {
	                			var thisOI = (payload.order.orderItems filter(($).sfId ~= (v).customFieldList.StringCustomFieldRef__custcol_salesforce_id.value))[0]
	                			---
	                			{
		                			"sfId": (
		                				if(!isEmpty(thisOI)) (
		                					thisOI.billingSchedule.billingScheduleDates[$$].sfId
		                				) else null
		                			),
		                			"nsId": ($).recurrenceId default null,
		                			"date": ($).recurrenceDate default null
	                			}
	                		}
                		)
                		---
                		{	
	                		"sfId": thisBSSfId default null, // thisBS.customFieldList.StringCustomFieldRef__custcol_salesforce_id.value default null,
	                		"nsId": (v).billingSchedule.@internalId default null,
	                		
	                		"billingScheduleDates": billingScheduleDates default [] map() -> ($ filterObject(!($$ ~= "date"))),
		                	
		                	"billingScheduleDetails": do {
		                		// bsdetail's name would be same as SfId of BillingSchedule + Date of BillingScheduleDate
			                	// BSDetail: "bsid123-23/03/2022" // BS: "bsid123" // BSDate's date: 23/03/2022
			                	var thisOI = (payload.order.orderItems filter(($).sfId ~= (v).customFieldList.StringCustomFieldRef__custcol_salesforce_id.value))[0]
			                	var billingScheduleDateList = billingScheduleDates.date map() -> (($) as Date as String {format: 'yyyy-MM-dd'}) default []
			                	var billingScheduleDetails = billingScheduleDetailRecords filter(
			                		billingScheduleDateList contains (substringAfter(($).record.name, (thisBS.name ++ "-")))
			                	)
			                	---
				               	billingScheduleDetailRecords filter(billingScheduleDateList contains (substringAfter(($).record.name, ((thisBS.name default "") ++ "-")))) map(bsDetailV, bsDetailK) -> {
			                		"bsDateSfId": (
		                				if(!isEmpty(thisOI)) (
		                					thisOI.billingSchedule.billingScheduleDates[bsDetailK].sfId
		                				) else null
		                			), // (bsDetailV).record.customFieldList.StringCustomFieldRef__custcol_billing_schedule_date_salesforce_id.value default null
			                		"nsId": (bsDetailV).record.@internalId default null
			                	}
			                }	
			            }
		            }
                }
            ) - {}) if(!isEmpty(orderRecord.itemList.*item))
        }
    }
}
else {
    "code": 400,
    "status": "FAILURE",
    "transactionId": vars.vTransactionId,
    "response": vars.vErrorBody
}]]></ee:set-payload>
				</ee:message>
				<ee:variables >
					<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
var orderRecord = vars.vSGFetchResponse.'0'.payload.getResponse.readResponse.record default {}
var billingScheduleRecords = vars.vSGFetchResponse.'1'.payload.getListResponse.readResponseList.*readResponse default []
output application/json
---
if(!isEmpty(orderRecord)) 200 else 400]]></ee:set-variable>
				</ee:variables>
			</ee:transform>
			<error-handler >
				<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="be5919a5-e3f9-47fd-b51f-e10ad3653174" type="ANY" >
					<logger level="ERROR" doc:name="LOG ERROR: Exception while updating SalesOrder" doc:id="3dd77e09-9987-4578-9c86-fc0d3ace7cee" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"BusinessKey" : vars.vBusinessKey,&#10;  	"Message" : "Exception while updating SalesOrder/BillingSchedule", &#10;	"FlowName" : "pf-update-netsuite-order", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"ErrorDescription": error.description&#10;}]' />
				</on-error-propagate>
			</error-handler>
		</try>
		<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Response Payload" doc:id="2ff70379-fd41-4d3a-9625-8c700162586e" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-update-netsuite-order",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BackendResponse": payload,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response" doc:id="7d1e7559-b89d-439c-9c0e-2c9d9cfe9640" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-update-netsuite-order",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "/api/orders",&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit" doc:id="c46031af-42d6-41f5-97c3-1e01982c50d7" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-update-netsuite-order",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"BusinessKey" : vars.vBusinessKey&#10;}]' />
	</flow>
</mule>
