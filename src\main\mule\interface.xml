<?xml version="1.0" encoding="UTF-8"?>
<mule xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:apikit="http://www.mulesoft.org/schema/mule/mule-apikit" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core" xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns:netsuite="http://www.mulesoft.org/schema/mule/netsuite" xmlns:tls="http://www.mulesoft.org/schema/mule/tls" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation=" http://www.mulesoft.org/schema/mule/tls http://www.mulesoft.org/schema/mule/tls/current/mule-tls.xsd http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd http://www.mulesoft.org/schema/mule/mule-apikit http://www.mulesoft.org/schema/mule/mule-apikit/current/mule-apikit.xsd http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd  http://www.mulesoft.org/schema/mule/netsuite http://www.mulesoft.org/schema/mule/netsuite/current/mule-netsuite.xsd">
    
    <flow name="3degreesNetsuiteSysAPI-main" doc:id="35cd68c8-f892-42a4-8175-d6601c75a970">
        <http:listener path="/api/*" doc:name="/api/*" config-ref="HTTPS_Listener_config">
            <http:response statusCode="#[vars.httpStatus default 200]">
                <http:headers><![CDATA[#[vars.outboundHeaders default {}]]]></http:headers>
            </http:response>
            <http:error-response statusCode="#[vars.httpStatus default 500]">
                <http:body><![CDATA[#[payload]]]></http:body>
                <http:headers><![CDATA[#[vars.outboundHeaders default {}]]]></http:headers>
            </http:error-response>
        </http:listener>
        <ee:transform doc:name="Set vCorrelationId, vTransactionId, vAttributes,vBusinessKey" doc:id="ec327116-1f56-4167-8a6f-fd1bc32da4a5">
            <ee:message />
            <ee:variables>
                <ee:set-variable variableName="vCorrelationId"><![CDATA[attributes.headers.'correlationId' default ""]]></ee:set-variable>
                <ee:set-variable variableName="vTransactionId"><![CDATA[attributes.headers.'x-transactionId' default ""]]></ee:set-variable>
                <ee:set-variable variableName="vAttributes"><![CDATA[%dw 2.0
output application/json
---
{
	"headers": attributes.headers,
	"queryParams": attributes.queryParams,
	"uriParams": attributes.uriParams
}]]></ee:set-variable>
                <ee:set-variable variableName="vBusinessKey"><![CDATA[attributes.headers.'x-businessKey' default ""]]></ee:set-variable>
            </ee:variables>
        </ee:transform>
        <apikit:router config-ref="3degreesNetsuiteSysAPI-config" />
        <flow-ref doc:name="Flow Reference to sf-remove-all-vars" doc:id="cb736a31-5999-4bc8-bc5a-87905511fffd" name="sf-remove-all-vars" />
        <error-handler ref="global-error-handler" />
    </flow>
    <flow name="put:\account:application\json:3degreesNetsuiteSysAPI-config" doc:id="7585404d-b6b1-4ecc-bd25-12037552e4bc">
        <set-variable value="#[&quot;ACCOUNT&quot;]" doc:name="vRecordType" doc:id="cde3fb8f-411e-4d94-830c-8ba46e3ec73c" variableName="vRecordType" />
        <flow-ref doc:name="Flow Reference to pf-update-netsuite-account" doc:id="602d7e4a-999e-411b-9a0b-53c369089ed0" name="pf-update-netsuite-account" />
    </flow>
    <flow name="get:\account:3degreesNetsuiteSysAPI-config" doc:id="41267e63-7a97-4e60-9542-91691f803e31">
        <set-variable value="#[&quot;ACCOUNT&quot;]" doc:name="vRecordType" doc:id="243342e7-b880-42a1-89ec-53db20276614" variableName="vRecordType" />
        <flow-ref doc:name="Flow Reference to pf-get-netsuite-account" doc:id="0e64a4f9-de8b-4c5b-83d7-c79f795a8d9f" name="pf-get-netsuite-account" />
    </flow>
    <flow name="post:\account:application\json:3degreesNetsuiteSysAPI-config" doc:id="bf31b288-a6ba-4c3c-9f32-810030919f2f">
        <set-variable value="#[&quot;ACCOUNT&quot;]" doc:name="vRecordType" doc:id="0fa8f446-85c6-4940-8505-1838fd0f7a2b" variableName="vRecordType" />
        <flow-ref doc:name="Flow Reference to pf-create-netsuite-account" doc:id="709d5f77-bd13-48ca-8b7e-e71b23c36f47" name="pf-create-netsuite-account" />
    </flow>
    <flow name="delete:\account:3degreesNetsuiteSysAPI-config">
        <set-variable value="#[&quot;ACCOUNT&quot;]" doc:name="vRecordType" doc:id="9df8564f-66f6-41ea-9614-4af40e73b8dd" variableName="vRecordType" />
        <flow-ref doc:name="Flow Reference to pf-delete-netsuite-account" doc:id="81e49295-67f1-4ce5-a115-d5a89251b54d" name="pf-delete-netsuite-account" />
    </flow>
    <flow name="put:\orders:application\json:3degreesNetsuiteSysAPI-config">
        <flow-ref doc:name="Flow Reference to pf-update-netsuite-order" doc:id="608ec53d-dbc6-43e8-a0ee-ec3a9214abb3" name="pf-update-netsuite-order" />
    </flow>
    <flow name="delete:\orders:3degreesNetsuiteSysAPI-config">
        <flow-ref doc:name="Flow Reference to pf-delete-netsuite-order" doc:id="9c9bb89c-9820-4435-9475-90bcb0062e6f" name="pf-delete-netsuite-order" />
    </flow>
    <flow name="get:\orders:3degreesNetsuiteSysAPI-config">
        <flow-ref doc:name="Flow Reference to pf-get-netsuite-order" doc:id="2b19c10c-3a43-4b4c-a881-079d2f1884ce" name="pf-get-netsuite-order" />
    </flow>
    <flow name="post:\orders:application\json:3degreesNetsuiteSysAPI-config">
        <flow-ref doc:name="Flow Reference to pf-create-netsuite-order" doc:id="a10c6ab9-1fdf-46c6-a593-890b8edac878" name="pf-create-netsuite-order" />
    </flow>
    <flow name="get:\lookup:3degreesNetsuiteSysAPI-config">
        <flow-ref doc:name="Flow Reference to pf-get-netsuite-generic-lookup-record" doc:id="a7ae6137-a77f-4adf-ab69-a4ef11da3555" name="pf-get-netsuite-generic-lookup-record" />
    </flow>
    <flow name="put:\invoices:application\json:3degreesNetsuiteSysAPI-config">
        <flow-ref doc:name="pf-update-netsuite-invoice" doc:id="20a29456-a541-4684-94ef-733606bae043" name="pf-update-netsuite-invoice" />
    </flow>
    <flow name="get:\invoices:3degreesNetsuiteSysAPI-config">
        <flow-ref doc:name="Flow Reference" doc:id="dcf77115-389f-4836-b5be-bca10d43ea0b" name="pf-get-netsuite-invoice" />
    </flow>
    <flow name="get:\lookup\savedSearch:3degreesNetsuiteSysAPI-config">
        <flow-ref doc:name="Flow Reference to pf-get-netsuite-saved-search-record" doc:id="d2729b0d-8dfc-4327-b928-e0a1819e55a7" name="pf-get-netsuite-saved-search-record" />
    </flow>
    <flow name="put:\product:application\json:3degreesNetsuiteSysAPI-config" doc:id="b237877e-c650-42f5-ad1a-bf1149ec488a">
        <set-variable value="#[&quot;PRODUCT&quot;]" doc:name="vRecordType" doc:id="b1b776da-c425-4595-890c-775daa8bb4f4" variableName="vRecordType" />
        <flow-ref doc:name="Flow Reference to pf-update-netsuite-account" doc:id="83c91bf0-30a6-498f-a97f-0382e0d6ea42" name="pf-update-netsuite-product" />
    </flow>
    <flow name="get:\product:3degreesNetsuiteSysAPI-config" doc:id="fab5f512-7d7a-4ed3-83f3-8e1919e88d26">
        <set-variable value="#[&quot;PRODUCT&quot;]" doc:name="vRecordType" doc:id="6da6f045-32f7-4e70-9007-683cc2cf2a0c" variableName="vRecordType" />
        <flow-ref doc:name="Flow Reference to pf-get-netsuite-product" doc:id="08409bbe-ff98-44d0-a34f-b2a2f95c2bd7" name="pf-get-netsuite-product" />
    </flow>
    <flow name="post:\product:application\json:3degreesNetsuiteSysAPI-config" doc:id="8017bb03-2d68-4a25-8592-bf232aeceecf">
        <set-variable value="#[&quot;PRODUCT&quot;]" doc:name="vRecordType" doc:id="8e21c793-907f-48ef-b2a4-13947344c38b" variableName="vRecordType" />
        <flow-ref doc:name="Flow Reference to pf-create-netsuite-product" doc:id="260193cf-eb98-4a4c-9275-c089218febfe" name="pf-create-netsuite-product" />
    </flow>
    <flow name="delete:\product:3degreesNetsuiteSysAPI-config">
        <set-variable value="#[&quot;PRODUCT&quot;]" doc:name="vRecordType" doc:id="2b0fb9b1-aff0-41fa-965e-878b9717c4a7" variableName="vRecordType" />
        <flow-ref doc:name="Flow Reference to pf-delete-netsuite-product" doc:id="ed3ded07-38c3-4e8a-81e0-49049deb50e5" name="pf-delete-netsuite-product" />
    </flow>
</mule>
