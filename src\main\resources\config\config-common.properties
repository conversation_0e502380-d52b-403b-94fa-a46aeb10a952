### HTTPS Listener ###
https.listener.host=0.0.0.0
https.listener.port=8081
https.listener.readTimeout=30000
https.listener.idleTimeout=30000

### Error definition ###
errorCodeMessage.apikit.badRequest.code=400
errorCodeMessage.apikit.badRequest.description=Bad Request
errorCodeMessage.apikit.notFound.code=404
errorCodeMessage.apikit.notFound.description=Not Found
errorCodeMessage.apikit.methodNotAllowed.code=405
errorCodeMessage.apikit.methodNotAllowed.description=Method Not Allowed
errorCodeMessage.apikit.notAcceptable.code=406
errorCodeMessage.apikit.notAcceptable.description=Not Acceptable
errorCodeMessage.apikit.unsupportedMediaType.code=415
errorCodeMessage.apikit.unsupportedMediaType.description=Unsupported Media Type
errorCodeMessage.apikit.notImplemented.code=501
errorCodeMessage.apikit.notImplemented.description=Not Implemented

### DB SYS API ###
https.request.dbSysApi.refId.path=/api/REF_ID
https.request.dbSysApi.transactionTaskDetails.path=/api/TRANSACTION_TASK

### Netsuite Generic Object lookup ###
Customer=CustomerSearchBasic
Employee=EmployeeSearchBasic
Order=TransactionSearchBasic
Invoice=TransactionSearchBasic
Opportunity=OpportunitySearchBasic

### Netsuite SavedSearch reference ### 
BusinessUnit=ClassificationSearchAdvanced
Division=DepartmentSearchAdvanced
Transaction=TransactionSearchAdvanced