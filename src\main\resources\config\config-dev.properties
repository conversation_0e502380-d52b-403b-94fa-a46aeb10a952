### API ###
api.autodiscoveryId=18611191

### HTTPS Listener ###
https.listener.keystore.path=certificates/keystore-dev.jks
https.listener.truststore.path=certificates/truststore-dev.jks

### Retry stategy ###
netsuite.retry.maxRetries=3
netsuite.retry.timePeriod=1000
netsuite.user.mulesoft.emailAddress=<EMAIL>
netsuite.wsdlVersion=V2021_1
netsuite.soapPort=services/NetSuitePort_2021_1
netsuite.signatureAlgorithm=HMAC_SHA256
netsuite.readTimeout=175000
netsuite.connectionTimeout=60000
netsuite.reconnection.frequency=10000
netsuite.reconnection.attempts=3
netsuite.invoiceRelatedRecords.savedSearchId=1764
netsuite.custRecord.billingScheduleDetails.type=__customRecordType__customrecord_custrecordentrylist__1435
netsuite.custRecord.billingScheduleDetails.internalId=1435

### HTTPS Request Transaction DB ###
https.request.transactionDBSysApi.host=transactiondb-sys-api-dev-kby5ju.internal-1avcn3.usa-w2.cloudhub.io
https.request.transactionDBSysApi.port=443
https.request.transactionDBSysApi.connectionTimeout=30000
https.request.transactionDBSysApi.responseTimeout=30000
https.request.transactionDBSysApi.reconnection.frequency=1000
https.request.transactionDBSysApi.reconnection.attempts=3
https.request.transactionDBSysApi.truststore.path=certificates/truststore-dev.jks
