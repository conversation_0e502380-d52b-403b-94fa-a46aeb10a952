%dw 2.0
output application/xml
ns ns0 urn:messages_2021_1.platform.webservices.netsuite.com
ns ns01 urn:relationships.lists.webservices.netsuite.com
ns ns02 urn:core_2021_1.platform.webservices.netsuite.com
ns ns03 urn:common.platform.webservices.netsuite.com
ns xsi http://www.w3.org/2001/XMLSchema-instance
---
{
	ns0#add: {
		ns01#record @(xsi#"type": "ns01:Customer"): {
			ns01#externalId: payload.account.'eid',
			ns01#companyName: payload.account.'companyname',
			ns01#isPerson: payload.account.'isperson' default "false",
			(ns01#entityStatus @(internalId: payload.account.'entitystatus'): null) if(!isEmpty(payload.account.'entitystatus')),
			(ns01#subsidiary @(internalId: payload.account.'subsidiary'): null) if(!isEmpty(payload.account.'subsidiary')),
			(ns01#customForm @(internalId: payload.account.'customform'): null) if(!isEmpty(payload.account.'customform')),
			ns01#addressbookList @(replaceAll: true): {
				ns01#addressbook: {
					ns01#defaultBilling: true,
					ns01#addressbookAddress: {
						ns03#addr1: payload.account.'addr1',
						ns03#addr2: payload.account.'addr2',
						ns03#city: payload.account.'city',
						ns03#state: payload.account.'state',
						ns03#zip: payload.account.'zip',
						ns03#country: payload.account.'country'
					}
				}
			},
			ns01#customFieldList: {
				StringCustomFieldRef__custentity_accountupdatedby: {
					ns02#value: payload.account.'accountupdatedby'
				}
			}
		}
	}
}