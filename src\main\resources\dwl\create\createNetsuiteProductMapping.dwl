%dw 2.0
output application/xml
ns ns0 urn:messages_2021_1.platform.webservices.netsuite.com
ns ns01 urn:accounting.lists.webservices.netsuite.com
ns ns02 urn:core_2021_1.platform.webservices.netsuite.com
ns xsi http://www.w3.org/2001/XMLSchema-instance
---
{
	ns0#add: {
		ns01#record @(xsi#"type": if(payload.product.productType == 'NonInventorySaleItem')"ns01:NonInventorySaleItem" else "ns01:ServiceSaleItem"): {
			ns01#taxSchedule @(internalId: "1"): {
				ns02#name: payload.product.taxSchedule
			},
			ns01#displayName: payload.product.displayName,
			ns01#salesDescription: payload.product.salesDescription,
			ns01#itemId: payload.product.'eid',
			ns01#customFieldList: {
				StringCustomFieldRef__custitem_item_updated_by: {
					ns02#value: payload.product.'itemupdatedby'
				}
			}
			
			}
		}
		
}