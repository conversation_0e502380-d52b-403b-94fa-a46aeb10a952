%dw 2.0
var orderRecord = vars.vSGFetchResponse.'0'.payload.getResponse.readResponse.record default {}
output application/xml
ns ns0 urn:messages.platform.webservices.netsuite.com
ns ns01 urn:customization.setup.webservices.netsuite.com
ns ns02 urn:core.platform.webservices.netsuite.com
ns xsi http://www.w3.org/2001/XMLSchema-instance
---
{
	ns0#asyncUpdateList: ((vars.vSGFetchResponse.'2'.payload.getListResponse.readResponseList.*readResponse default []) map() -> {
		ns0#record @("xmlns:ns01": ns01, xsi#"type": "ns01:CustomRecord", ("internalId": ($).record.@internalId)): {
			ns01#customFieldList: {
				(SelectCustomFieldRef__custrecord_sonumb: {
					ns02#value @(internalId: orderRecord.@internalId): {}
				}) if(!isEmpty(orderRecord.@internalId))
			}
		}
	}) reduce($$ ++ $)
}