%dw 2.0
import * from dw::Runtime
import * from dw::core::Strings

var orderRecord = vars.vSGFetchResponse.'0'.payload.getResponse.readResponse.record default {}
var billingScheduleRecords = vars.vSGFetchResponse.'1'.payload.getListResponse.readResponseList.*readResponse.*record default []
var billingScheduleDetailRecords = vars.vSGFetchResponse.'2'.payload.getListResponse.readResponseList.*readResponse.*record default []

fun getErrorDesc(sgRespArr) = (
	try(
        () -> (
            sgRespArr map() -> (
                if(!isEmpty(($).payload.description)) (
                    fail(($).payload.description)
                )
                else null
            )
        )
    )
)

output application/json
---
if(!isEmpty(orderRecord)) {
	"code": 200,
    "status": "SUCCESS",
    "transactionId": vars.vTransactionId,
	"response": {
        "order": {
        	"internalId": orderRecord.@internalId default null,
            "sfId": orderRecord.customFieldList.StringCustomFieldRef__custbody_salesforce_id.value,
            "tranId": orderRecord.tranId default null,
            "customForm": orderRecord.customForm.@internalId default null,
            "entity": orderRecord.entity.@internalId default null,
            "tranDate": orderRecord.tranDate default null,
            "opportunityId": orderRecord.customFieldList.StringCustomFieldRef__custbody_opportunity_id.value default null,
            "otherRefNum": orderRecord.otherRefNum default null,
            "orderNumber": orderRecord.customFieldList.StringCustomFieldRef__custbody_salesforce_order_number.value default null,
            "subsidiary": orderRecord.subsidiary.@internalId default null,
            "memo": orderRecord.memo default null,
            "currency": orderRecord.currency.@internalId  default null,
            "orderStatus": orderRecord.orderStatus default null,
            "discountItem": orderRecord.discountItem.@internalId default null,
            "discountRate": orderRecord.discountRate default null,
            "startDate": orderRecord.startDate default null,
            "endDate": orderRecord.endDate default null,
            "paymentEventUpdatedBy": orderRecord.paymentEventUpdatedBy.@internalId default null,
            "paymentEventDate": orderRecord.paymentEventDate default null,
            "billingAddress": {
                "addr1": orderRecord.billingAddress.addr1 default null,
                "addr2": orderRecord.billingAddress.addr2 default null,
                "city": orderRecord.billingAddress.city default null,
                "country": orderRecord.billingAddress.country default null,
                "state": orderRecord.billingAddress.state default null,
                "zip": orderRecord.billingAddress.zip default null,
                "label": orderRecord.billingAddress.label default null,
            },
            "defaultBilling": orderRecord.defaultbilling default true,
            "fulfillmentModel": orderRecord.customFieldList.StringCustomFieldRef__custbody_fulfillment_model.value default null,
            "taxRate": orderRecord.taxRate default null,
            "taxItem": orderRecord.taxItem.@internalId default null,
            "artemisConfirmId": orderRecord.customFieldList.StringCustomFieldRef__custbody_3d_confirm_id.value default null,
            "total": (orderRecord.total default "0") as Number,
            "subTotal": (orderRecord.subTotal default "0") as Number,
            "billingEventCategory": orderRecord.customFieldList.StringCustomFieldRef__custbody_billingeventcategory.value default null,
            "email": orderRecord.email default null,
            "toBeCopied": orderRecord.customFieldList.StringCustomFieldRef__custbody_tobecopied.value default null,
            "commoditiesSold": (orderRecord.customFieldList.BooleanCustomFieldRef__custbody_commodities_sold.value default "false") as Boolean,
            ("orderItems": [] ++ (
                orderRecord.itemList.*item map() -> {
                	"line": ($).line default null,
                	"sfId": ($).customFieldList.StringCustomFieldRef__custcol_salesforce_id.value default null,
                	"item": ($).item.@'internalId' default null,
                    "description": ($).description default null,
                    "department": ($).department.@internalId default null,
                    "class": ($).class.@internalId default null,
                    "cseg3dGeoTag": ($).customFieldList.SelectCustomFieldRef__cseg_3d_geo_tag.value.@internalId default null,
                    "quantity": (($).quantity default "0") as Number,
                    "artemisPositionId": ($).customFieldList.StringCustomFieldRef__custcol_3d_position_id.value default null,
                    "rate": (($).rate default "0") as Number,
                    "amount": (($).amount default "0") as Number,
                    "isTaxable": ($).isTaxable default false,
                    "revStartDate": ($).revStartDate default null,
                    "revEndDate": ($).revEndDate default null,
                    "price": ($).price.@internalId default null,
                    "fulfillable": ($).fulfillable default true,
                    "isClosed": ($).isClosed default false,
                    "isEstimate": ($).isEstimate default false,
                    "isOpen": ($).isOpen default true,
                    "isNonInventory": ($).isNonInventory default true,
                    "isPosting": ($).isPosting default true,
                    "chargeType": ($).chargeType default null,
                    "cancellationCountry": ($).customFieldList.SelectCustomFieldRef__custcol_cancellationcountry.value.name default null,
                    "consumptionCountry": ($).customFieldList.SelectCustomFieldRef__custcol_consumptioncountry.value.name default null,
                    ("billingSchedule": do {
                    	var billingScheduleRecord = (billingScheduleRecords filter((item, index) -> (item).@internalId ~= ($).billingSchedule.@internalId))[0]
                    	---
                    	{
			            	"internalId": billingScheduleRecord.@internalId default null,
			            	"sfId": null,
			            	"scheduleType": billingScheduleRecord.scheduleType default null,
			            	"name": billingScheduleRecord.name default null,
			            	"initialAmount": (billingScheduleRecord.initialAmount default "0") as Number,
			            	"initialTerms": billingScheduleRecord.initialTerms.@internalId default null,
			            	"frequency": billingScheduleRecord.frequency default null,
			            	"repeatEvery": billingScheduleRecord.repeatEvery default null,
			            	"numberRemaining": billingScheduleRecord.numberRemaining default null,
			            	"inArrears": billingScheduleRecord.inArrears default null,
			            	"recurrenceTerms": billingScheduleRecord.recurrenceTerms default null,
			            	"isPublic": billingScheduleRecord.isPublic default null,
			            	"billingScheduleDates": billingScheduleRecord.recurrenceList.*billingScheduleRecurrence map() -> {
			            		"recurrenceId": ($).recurrenceId default null,
			            		"sfId": null,
			            		"count": (($).count default "0") as Number,
			            		"units": ($).units default null,
			            		"relativeToPrevious": ($).relativeToPrevious default null,
			            		"recurrenceDate": ($).recurrenceDate default null,
			            		"amount": ($).amount default "0",
			            		"paymentTerms": ($).paymentTerms.@internalId default null
			            	},
			            	"billingScheduleDetails": (billingScheduleDetailRecords filter((item, index) -> substringBefore((item).name, "-") ~= billingScheduleRecord.name)) map() -> {
			            		"bsDetailNsId": ($).@internalId default null,
			            		"name": ($).name default null,
			            		"recType": ($).recType.name default null,
			            		"artemisInvoiceMemo": ($).customFieldList.StringCustomFieldRef__custrecord_im.value default null,
			            		"artemisPONumber": ($).customFieldList.StringCustomFieldRef__custrecord_ponumb.value default null,
			            		"bsDate": ($).customFieldList.DateCustomFieldRef__custrecord_bsdate.value default null,
			            		"bsName": ($).customFieldList.StringCustomFieldRef__custrecord_bsname.value default null,
			            		"salesOrderNumber": ($).customFieldList.SelectCustomFieldRef__custrecord_sonumb.value.name default null,
			            		"artemisInvoiceId": ($).customFieldList.StringCustomFieldRef__custrecord_artid.value
			            	}
			           	}
		            }) if(!isEmpty(($).billingSchedule))
                }
            ) - {}) if(!isEmpty(orderRecord.itemList.*item)),
        }
    }
}
else {
    "code": 200,
    "status": "SUCCESS",
    "transactionId": vars.vTransactionId,
    "response": {
      "message": "RECORD_NOT_FOUND",
      "details": getErrorDesc(vars.vSGFetchResponse pluck($)).error.message
    }
}
