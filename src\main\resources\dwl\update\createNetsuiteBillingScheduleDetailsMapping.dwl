%dw 2.0
output application/xml
ns ns0 urn:messages.platform.webservices.netsuite.com
ns ns01 urn:customization.setup.webservices.netsuite.com
ns ns02 urn:core.platform.webservices.netsuite.com
ns xsi http://www.w3.org/2001/XMLSchema-instance
---
{
	ns0#addList: (flatten(flatten(payload.order.orderItems.billingSchedule).billingScheduleDetails) distinctBy(($).'name') filter(($).operation ~= "CREATE") map() -> {
		ns0#record @("xmlns:ns01": ns01, xsi#"type": "ns01:CustomRecord"): {
			ns01#name: ($).name,
			ns01#customFieldList: {
				(StringCustomFieldRef__custrecord_im: {
					ns02#value: ($).artemisInvoiceMemo
				}) if(!isEmpty(($).artemisInvoiceMemo)),
				(StringCustomFieldRef__custrecord_ponumb: {
					ns02#value: ($).artemisPONumber
				}) if(!isEmpty(($).artemisPONumber)),
				("DateCustomFieldRef__custrecord_bsdate": {
					ns02#value: ($).bsDate
				}) if(!isEmpty(($).bsDate)),
				(StringCustomFieldRef__custrecord_bsname: {
					ns02#value: ($).bsName
				}) if(!isEmpty(($).bsName)),
				(SelectCustomFieldRef__custrecord_sonumb: {
					ns02#value @(internalId: vars.vInternalId): {}
				}) if(!isEmpty(vars.vInternalId)),
				(StringCustomFieldRef__custrecord_artid: {
					ns02#value: ($).artemisInvoiceId
				}) if(!isEmpty(($).artemisInvoiceId)),
				(StringCustomFieldRef__custrecord_artinvorderid: {
					ns02#value: ($).artemisInvoiceOrderId
				}) if(!isEmpty(($).artemisInvoiceOrderId)),
			}
		}
	}) reduce($$ ++ $)
}