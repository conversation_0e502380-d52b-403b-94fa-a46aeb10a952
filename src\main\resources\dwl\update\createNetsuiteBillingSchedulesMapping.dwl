%dw 2.0
output application/xml
ns ns0 urn:messages.platform.webservices.netsuite.com
ns ns01 urn:accounting.lists.webservices.netsuite.com
ns ns02 urn:core.platform.webservices.netsuite.com
---
{
	ns0#addList: ((payload.order.orderItems.billingSchedule distinctBy(($).'name') filter(($).operation ~= "CREATE")) map() -> {
		ns0#record @("xmlns:ns0": ns0,"xmlns:ns01": ns01, xsi#"type": "ns01:BillingSchedule"): {
			(ns01#scheduleType: ($).'scheduleType') if(!isEmpty(($).'scheduleType')),
			(ns01#name: ($).'name') if(!isEmpty(($).'name')),
			(ns01#initialAmount: ($).'initialAmount') if(!isEmpty(($).'initialAmount')),
			(ns01#initialTerms @(internalId: ($).'initialTerms'): {}) if(!isEmpty(($).'initialTerms')),
			(ns01#frequency: ($).'frequency') if(!isEmpty(($).'frequency')),
			(ns01#repeatEvery: ($).'repeatEvery') if(!isEmpty(($).'repeatEvery')),
			(ns01#numberRemaining: ($).'numberRemaining') if(!isEmpty(($).'numberRemaining')), // recurrence
			(ns01#inArrears: ($).inArrears) if(!isEmpty(($).inArrears)),
			(ns01#recurrenceTerms @(internalId: ($).'recurrenceTerms'): {}) if(!isEmpty(($).'recurrenceTerms')),
			(ns01#isPublic: ($).'isPublic') if(!isEmpty(($).'isPublic')),
			(ns01#isInactive: ($).'isInactive') if(!isEmpty(($).'isInactive')),
			ns01#recurrenceList: (($).billingScheduleDates map() -> {
				ns01#billingScheduleRecurrence: do {
					// vars.vSFNSMapping: {"sf_id_of_some_bsdetail": "sf_id_of_same_bsdetail"}
					var billingScheduleDetailNSId = vars.vSFNSMapping[(($).billingScheduleDetail.sfId default "")] default null
					---
					{
						(ns01#count: ($).'count') if(!isEmpty(($).'count')),
						(ns01#units: ($).'units') if(!isEmpty(($).'units')),
						(ns01#relativeToPrevious: ($).'relativeToPrevious') if(!isEmpty(($).'relativeToPrevious')),
						(ns01#recurrenceDate: ($).'recurrenceDate') if(!isEmpty(($).'recurrenceDate')),
						(ns01#amount: ($).'amount') if(!isEmpty(($).'amount')),
						(ns01#paymentTerms @(internalId: ($).'paymentTerms'): {}) if(!isEmpty(($).'paymentTerms')),
						(ns01#billingScheduleDetail @(internalId: billingScheduleDetailNSId): {}) if(!isEmpty(billingScheduleDetailNSId))
					}
				}
			}) reduce($$ ++ $)
		}
	}) reduce($$ ++ $)
}