%dw 2.0
output application/xml
ns ns0 urn:messages_2021_1.platform.webservices.netsuite.com
ns ns01 urn:common_2021_1.platform.webservices.netsuite.com
ns ns02 urn:core_2021_1.platform.webservices.netsuite.com
ns ns03 urn:relationships_2021_1.lists.webservices.netsuite.com
ns ns04 urn:sales.transactions.webservices.netsuite.com
ns xsi http://www.w3.org/2001/XMLSchema-instance
---
{
	ns0#deleteList: (payload.order.orderItems.billingSchedule filter(($).operation ~= "DELETE") map() -> {
		ns0#baseRef @("xmlns:ns0": ns0, "xmlns:ns02": ns02, xsi#"type": "ns02:RecordRef", "type": "billingSchedule", ("internalId": ($).netsuiteId)): null
	}) reduce($$ ++ $)
}