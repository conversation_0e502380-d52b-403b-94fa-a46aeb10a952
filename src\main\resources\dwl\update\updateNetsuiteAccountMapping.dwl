%dw 2.0
output application/xml
ns ns0 urn:messages_2021_1.platform.webservices.netsuite.com
ns ns01 urn:relationships.lists.webservices.netsuite.com
ns ns02 urn:core_2021_1.platform.webservices.netsuite.com
ns ns03 urn:common.platform.webservices.netsuite.com
ns xsi http://www.w3.org/2001/XMLSchema-instance
---
if((vars.vAttributes.headers.'context' != "PARTIAL_UPDATE")) {
	ns0#update: {
		ns01#record @(xsi#"type": "ns01:Customer",  "internalId": (vars.vInternalId as Number)): {
			ns01#externalId: payload.account.'eid',
			ns01#companyName: payload.account.'companyname',
			ns01#addressbookList @(replaceAll: true): {
				ns01#addressbook: {
					ns01#internalId: vars.vRetrieveCustomerResponse.getResponse.readResponse.record.addressbookList.addressbook.internalId default "",
					ns01#defaultBilling: true,
					ns01#addressbookAddress: {
						ns03#addr1: payload.account.'addr1',
						ns03#addr2: payload.account.'addr2',
						ns03#city: payload.account.'city',
						ns03#state: payload.account.'state',
						ns03#zip: payload.account.'zip',
						ns03#country: payload.account.'country'
					}
				}
			},
			ns01#customFieldList: {
				StringCustomFieldRef__custentity_accountupdatedby: {
					ns02#value: payload.account.'accountupdatedby'
				},
				StringCustomFieldRef__custentity_initiatedelete: {
					ns02#value: payload.account.'initiatedelete'
				},
				LongCustomFieldRef__custentity_syncpriority: {
					ns02#value: payload.account.'syncpriority'
				},
				StringCustomFieldRef__custentity_syncmessage: {
					ns02#value: payload.account.'syncMessage'
				},
				SelectCustomFieldRef__custentity_customersyncmessage: {
                    ns02#value @(internalId: payload.account.'syncStatus'): {}
                }
			}
		}
	}
} else {
	ns0#update: {
		ns01#record @(xsi#"type": "ns01:Customer",  "internalId": (vars.vInternalId as Number)): {
			(ns01#externalId: payload.account.'eid') if(!isEmpty(payload.account.'eid')),
			(ns01#companyName: payload.account.'companyname') if(!isEmpty(payload.account.'companyname')),
			ns01#addressbookList @(replaceAll: true): {
				ns01#addressbook: {
					ns01#internalId: vars.vRetrieveCustomerResponse.getResponse.readResponse.record.addressbookList.addressbook.internalId default "",
					ns01#defaultBilling: true,
					ns01#addressbookAddress: {
						(ns03#addr1: payload.account.'addr1') if(!isEmpty(payload.account.'addr1')),
						(ns03#addr2: payload.account.'addr2') if(!isEmpty(payload.account.'addr2')),
						(ns03#city: payload.account.'city') if(!isEmpty(payload.account.'city')),
						(ns03#state: payload.account.'state') if(!isEmpty(payload.account.'state')),
						(ns03#zip: payload.account.'zip') if(!isEmpty(payload.account.'zip')),
						(ns03#country: payload.account.'country') if(!isEmpty(payload.account.'country')),
					}
				}
			},
			ns01#customFieldList: {
				(StringCustomFieldRef__custentity_accountupdatedby: {
					ns02#value: payload.account.'accountupdatedby'
				}) if(!isEmpty(payload.account.'accountupdatedby')),
				(StringCustomFieldRef__custentity_initiatedelete: {
					ns02#value: payload.account.'initiatedelete'
				}) if(!isEmpty(payload.account.'initiatedelete')),
				(LongCustomFieldRef__custentity_syncpriority: {
					ns02#value: payload.account.'syncpriority'
				}) if(!isEmpty(payload.account.'syncpriority')),
				(StringCustomFieldRef__custentity_syncmessage: {
					ns02#value: payload.account.'syncMessage'
				}) if(!isEmpty(payload.account.'syncMessage')),
				(SelectCustomFieldRef__custentity_customersyncmessage: {
                    ns02#value @(internalId: payload.account.'syncStatus'): {}
                }) if(!isEmpty(payload.account.'syncStatus'))
			}
		}
	}
}