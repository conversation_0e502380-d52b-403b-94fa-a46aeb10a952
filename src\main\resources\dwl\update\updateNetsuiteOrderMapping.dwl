%dw 2.0

var billingSchedules = if(!isEmpty(vars.vSGBillingSchedulesResponse.'0'.payload)) vars.vSGBillingSchedulesResponse.'0'.payload.addListResponse.writeResponseList.*writeResponse default [] else null
var distinctBillingSchedules = (payload.order.orderItems.billingSchedule distinctBy(($).'name')).'name'

output application/xml skipNullOn="everywhere"
ns ns0 urn:messages.platform.webservices.netsuite.com
ns ns01 urn:sales.transactions.webservices.netsuite.com
ns ns02 urn:core.platform.webservices.netsuite.com
ns ns03 urn:common.platform.webservices.netsuite.com
---
{
	ns0#update: {
		ns0#record @("xmlns:ns0": ns0,"xmlns:ns01": ns01, xsi#"type": "ns01:SalesOrder", ("internalId": vars.vInternalId)): {
			(ns01#entity @(internalId: payload.order.'entity'): {}) if(!isEmpty(payload.order.'entity')),
            (ns01#entityTaxRegNum: payload.order.entityTaxRegNum) if(!isEmpty(payload.order.entityTaxRegNum)),
            (ns01#paymentEventUpdatedBy @(internalId: payload.order.paymentEventUpdatedBy): {}) if(!isEmpty(payload.order.paymentEventUpdatedBy)),
            (ns01#paymentEventDate: payload.order.paymentEventDate) if(!isEmpty(payload.order.paymentEventDate)),
            (
                ns01#billingAddress: {
                    (ns03#addr1: payload.order.billingAddress.'addr1') if(!isEmpty(payload.order.billingAddress.'addr1')),
                    (ns03#addr2: payload.order.billingAddress.'addr2') if(!isEmpty(payload.order.billingAddress.'addr2')),
                    (ns03#city: payload.order.billingAddress.'city') if(!isEmpty(payload.order.billingAddress.'city')),
                    (ns03#country @(internalId: payload.order.billingAddress.'country'): {}) if(!isEmpty(payload.order.billingAddress.'country')),
                    (ns03#state: payload.order.billingAddress.'state') if(!isEmpty(payload.order.billingAddress.'state')),
                    (ns03#zip: payload.order.billingAddress.'zip') if(!isEmpty(payload.order.billingAddress.'zip')),
                    (ns03#label: payload.order.billingAddress.'label') if(!isEmpty(payload.order.billingAddress.'label')) 
                }
            ) if(!isEmpty(payload.order.billingAddress)),
            (ns01#shipAddressList @(internalId: payload.order.shipAddressList): {}) if(!isEmpty(payload.order.shipAddressList)),
            // (ns01#revRecEndDate: payload.order.revRecEndDate) if(!isEmpty(payload.order.revRecEndDate)),
            // (ns01#createdFrom @(internalId: payload.order.createdFrom): {}) if(!isEmpty(payload.order.createdFrom)),
            (ns01#currency @(internalId: payload.order.currency default "1"): {}) if(!isEmpty(payload.order.currency)),
            (ns01#memo: payload.order.memo) if(!isEmpty(payload.order.memo)),
            // (ns01#opportunity @(internalId: payload.order.opportunityId): {}) if(!isEmpty(payload.order.opportunityId)),
            // (ns01#total: payload.order.'total') if(!isEmpty(payload.order.'total')),
            (ns01#endDate: payload.order.'endDate') if(!isEmpty(payload.order.'endDate')),
            // (ns01#tranId: payload.order.'tranId') if(!isEmpty(payload.order.'tranId')),
            (ns01#customForm @(internalId: payload.order.customForm): {}) if(!isEmpty(payload.order.customForm)),
            (ns01#otherRefNum: payload.order.otherRefNum) if(!isEmpty(payload.order.otherRefNum)),
            (ns01#generateTranIdOnSave: payload.order.generateTranIdOnSave) if(!isEmpty(payload.order.generateTranIdOnSave)),
            (ns01#subsidiary @(internalId: payload.order.subsidiary): {}) if(!isEmpty(payload.order.subsidiary)),
            (ns01#salesEffectiveDate: payload.order.salesEffectiveDate) if(!isEmpty(payload.order.salesEffectiveDate)),
            (ns01#discountItem @(internalId: payload.order.discountItem): {}) if(!isEmpty(payload.order.discountItem)),
            (ns01#discountRate: payload.order.discountRate) if(!isEmpty(payload.order.discountRate)),
            (ns01#defaultBilling: payload.order.defaultBilling) if(!isEmpty(payload.order.defaultBilling)),
            (ns01#taxItem @(internalId: payload.order.taxItem): {}) if(!isEmpty(payload.order.taxItem)),
            (ns01#taxRate: payload.order.taxRate) if(!isEmpty(payload.order.taxRate)),
            (ns01#startDate: payload.order.startDate) if(!isEmpty(payload.order.startDate)), 
            (ns01#endDate: payload.order.endDate) if(!isEmpty(payload.order.endDate)), 
            (ns01#createdFrom @(internalId: payload.order.createdFrom): {}) if(!isEmpty(payload.order.createdFrom)), 
            (ns01#salesRep @(internalId: payload.order.salesRep): {}) if(!isEmpty(payload.order.salesRep)), 
            (ns01#tranDate: payload.order.tranDate) if(!isEmpty(payload.order.tranDate)),

            (
                ns01#shippingAddress: {
                    (ns03#addr1: payload.order.shippingAddress.'addr1') if(!isEmpty(payload.order.shippingAddress.'addr1')),
                    (ns03#addr2: payload.order.shippingAddress.'addr2') if(!isEmpty(payload.order.shippingAddress.'addr2')),
                    (ns03#city: payload.order.shippingAddress.'city') if(!isEmpty(payload.order.shippingAddress.'city')),
                    (ns03#country @(internalId: payload.order.shippingAddress.'country'): {}) if(!isEmpty(payload.order.shippingAddress.'country')),
                    (ns03#state: payload.order.shippingAddress.'state') if(!isEmpty(payload.order.shippingAddress.'state')),
                    (ns03#zip: payload.order.shippingAddress.'zip') if(!isEmpty(payload.order.shippingAddress.'zip'))
                }
            ) if(!isEmpty(payload.order.shippingAddress)),

            (ns01#shipAddressList @(internalId: payload.order.shipAddressList): {}) if(!isEmpty(payload.order.shipAddressList)),

            (ns01#orderStatus: payload.order.orderStatus) if(!isEmpty(payload.order.orderStatus)),

			(ns01#total: payload.order.total) if(!isEmpty(payload.order.total)),
			
			(ns01#subTotal: payload.order.subTotal) if(!isEmpty(payload.order.subTotal)),

			(ns01#email: payload.order.email) if(!isEmpty(payload.order.email)),

            ns01#customFieldList: {
            	
            	(StringCustomFieldRef__custbody_salesforce_id: {
                	ns02#value: payload.order.sfId
                }) if(!isEmpty(payload.order.sfId)),
                (StringCustomFieldRef__custbody_salesforce_order_number: {
                	ns02#value: payload.order.orderNumber
                }) if(!isEmpty(payload.order.orderNumber)),
                (StringCustomFieldRef__custbody_fulfillment_model: {
                	ns02#value: payload.order.fulfillmentModel
                }) if(!isEmpty(payload.order.fulfillmentModel)),
                (StringCustomFieldRef__custbody_3d_confirm_id: {
                	ns02#value: payload.order.artemisConfirmId
                }) if(!isEmpty(payload.order.artemisConfirmId)),
                (StringCustomFieldRef__custbody_opportunity_id: {
                	ns02#value: payload.order.opportunityId
                }) if(!isEmpty(payload.order.opportunityId)),
                (StringCustomFieldRef__custbody_billingeventcategory: {
                	ns02#value: payload.order.billingEventCategory
                }) if(!isEmpty(payload.order.billingEventCategory)),
                (StringCustomFieldRef__custbody_tobecopied: {
                	ns02#value: payload.order.toBeCopied
                }) if(!isEmpty(payload.order.toBeCopied)),
                (BooleanCustomFieldRef__custbody_commodities_sold: {
                	ns02#value: payload.order.commoditiesSold
                }) if(!isEmpty(payload.order.commoditiesSold)),
                
                /* ("SelectCustomFieldRef__custentity_company_authorized_by_id": {
                    ns02#value @(internalId: payload.order.customFieldList.companyAuthorizedById): {}
                }) if(!isEmpty(payload.order.customFieldList.companyAuthorizedById)),
                ("DateCustomFieldRef__custentity_company_authorized_date": payload.order.customFieldList.companyAuthorizedDate) if(!isEmpty(payload.order.customFieldList.companyAuthorizedDate)),
                ("StringCustomFieldRef__custentity_contract_name": payload.order.customFieldList.contractName) if(!isEmpty(payload.order.customFieldList.contractName)),
                ("SelectCustomFieldRef__custentity_contract_id": {
                    ns02#value @(internalId: payload.order.customFieldList.contractId): {}
                }) if(!isEmpty(payload.order.customFieldList.contractId)),
                ("SelectCustomFieldRef__custentity_customer_authorized_by_id": {
                    ns02#value @(internalId: payload.order.customFieldList.customerAuthorizedById): {}
                }) if(!isEmpty(payload.order.customFieldList.customerAuthorizedById)),
                ("DateCustomFieldRef__custentity_customer_authorized_date": payload.order.customFieldList.customerAuthorizedDate) if(!isEmpty(payload.order.customFieldList.customerAuthorizedDate)),
                ("SelectCustomFieldRef__custentity_type": {
                    ns02#value @(internalId: payload.order.customFieldList.'type'): {}
                }) if(!isEmpty(payload.order.customFieldList.'type')),
                ("StringCustomFieldRef__custentity_po_number": payload.order.customFieldList.poNumber) if(!isEmpty(payload.order.customFieldList.poNumber)), */
            },
            (
                ns01#itemList: {
                    ns01#item: do {
                    	var allOrderItems = (
                    		payload.order.'orderItems' map() -> {
		                        (ns01#line: ($).'netsuiteId') if(!isEmpty(($).'netsuiteId')),
		                        (ns01#item @(internalId: ($).'item' as Number as String): {}) if(!isEmpty(($).'item')),
		                        (ns01#price @(internalId: ($).'price'): {}) if(!isEmpty(($).'price')),
								(ns01#rate : ($).'rate') if (!isEmpty(($).'rate')),
		                        (ns01#quantity: ($).'quantity') if (!isEmpty(($).'quantity')),
		                        (ns01#billingSchedule @(internalId: ($).billingSchedule.name): {}) if(!isEmpty(($).billingSchedule.name)), // this is mapped temporarily to understand if billingSchedule for this OrderItem != null...
		                        (ns01#chargeType: ($).'chargeType') if(!isEmpty(($).'chargeType')),
		                        (ns01#isPosting: ($).'isPosting') if(!isEmpty(($).'isPosting')),
		                        (ns01#isNonInventory: ($).'isNonInventory') if(!isEmpty(($).'isNonInventory')),
		                        (ns01#isEstimate: ($).'isEstimate') if(!isEmpty(($).'isEstimate')),
		                        (ns01#isClosed: ($).'isClosed') if(!isEmpty(($).'isClosed')),
		                        (ns01#isOpen: ($).'isOpen') if(!isEmpty(($).'isOpen')),
		                        (ns01#fulfillable: ($).'fulfillable') if(!isEmpty(($).'fulfillable')),
		                        (ns01#isTaxable: ($).'isTaxable') if(!isEmpty(($).'isTaxable')),
		                        (ns01#amount: ($).'amount') if(!isEmpty(($).'amount')),
		                        // (ns01#cseg_3d_geo_tag: ($).'cseg3dGeoTag') if(!isEmpty(($).'cseg3dGeoTag')),
		                        (ns01#class: ($).'class') if(!isEmpty(($).'class')),
		                        (ns01#department: ($).'department') if(!isEmpty(($).'department')),
								//(ns01#cseg_dept_new: ($).'csegDeptNew') if(!isEmpty(($).'csegDeptNew')),
		                        (ns01#description: ($).'description') if(!isEmpty(($).'description')),
		                        (ns01#revStartDate: ($).'revStartDate') if(!isEmpty(($).'revStartDate')),
		                        (ns01#revEndDate: ($).'revEndDate') if(!isEmpty(($).'revEndDate')),
		                        (
		                            ns01#customFieldList: {
		                                (StringCustomFieldRef__custcol_salesforce_id: {
		                                    ns02#value: ($).sfId
		                                })  if(!isEmpty(($).sfId)),
		                                
		                                (StringCustomFieldRef__custcol_3d_position_id: {
		                                    ns02#value: ($).artemisPositionId
		                                })  if(!isEmpty(($).artemisPositionId)),
		                                
		                                (SelectCustomFieldRef__cseg_3d_geo_tag @(internalId: "2821"): {
		                                    ns02#value @(internalId: ($).cseg3dGeoTag): {}
		                                })  if(!isEmpty(($).cseg3dGeoTag)),
										
										(SelectCustomFieldRef__cseg_dept_new: {
		                                    ns02#value @(internalId: ($).csegDeptNew): {}
		                                })  if(!isEmpty(($).csegDeptNew)),
		                                
		                                (SelectCustomFieldRef__custcol_cancellationcountry: {
		                                    ns02#value @(internalId: ($).cancellationCountry): {}
		                                })  if(!isEmpty(($).cancellationCountry)),
		                                
		                                (SelectCustomFieldRef__custcol_consumptioncountry: {
		                                    ns02#value @(internalId: ($).consumptionCountry): {}
		                                })  if(!isEmpty(($).consumptionCountry)),
	                            	}
		                        )
	                    	}
	                    )
	                    
	                    var emptyBSOrderItems = allOrderItems filter(isEmpty(($).billingSchedule.@internalId))
	                    ---
	                    flatten(
	                    	((allOrderItems) filter(!isEmpty(($).billingSchedule.@internalId)) map(v, i) -> do {
		                    	var indexOfThisOiBs = indexOf(distinctBillingSchedules, (v).billingSchedule.@internalId)
								---
		                    	((v) update {
		                    		case billingSchedule at .billingSchedule.@internalId -> billingSchedules[indexOfThisOiBs].baseRef.@internalId
		                    	}) filterObject(
								    !(($$) ~= "billingSchedule") or
								    !isEmpty($.@internalId)
								)
                    		}) ++ emptyBSOrderItems
                    	)
                    }
                }
            ) if(!isEmpty(payload.order.'orderItems')),
            /* (
            	ns01#billingSchedule @(internalId: vars.vCreateBillingScheduleResponse.addResponse.writeResponse.baseRef.@internalId default "-1"): {}
            ) if(!isEmpty(payload.order.billingSchedule)) */
        }
    }
}
