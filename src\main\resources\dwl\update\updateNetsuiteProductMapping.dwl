%dw 2.0
output application/xml
ns ns0 urn:messages_2021_1.platform.webservices.netsuite.com
ns ns01 urn:accounting.lists.webservices.netsuite.com
ns ns02 urn:core_2021_1.platform.webservices.netsuite.com
ns xsi http://www.w3.org/2001/XMLSchema-instance
---
if((vars.vAttributes.headers.'context' != "PARTIAL_UPDATE"))
{
	ns0#update: {
		ns01#record @(xsi#"type": if(vars.vProductType == 'NonInventorySaleItem')"ns01:NonInventorySaleItem" else "ns01:ServiceSaleItem", "internalId": (vars.vInternalId as Number)): {
			ns01#taxSchedule @(internalId: "1"): {
				ns02#name: payload.product.taxSchedule
			},
			ns01#displayName: payload.product.displayName,
			ns01#salesDescription: payload.product.salesDescription,
			ns01#itemId: payload.product.'eid',
			ns01#customFieldList: {
				StringCustomFieldRef__custitem_syncmessage: {
					ns02#value: payload.product.'custitem_syncmessage'
				},
				SelectCustomFieldRef__custitem_itemsyncstatus: {
                    ns02#value @(internalId: payload.product.'custitem_itemsyncstatus'): {}
                },
				StringCustomFieldRef__custitem_item_updated_by: {
					ns02#value: payload.product.'itemupdatedby'
				}
			}
			
			}
		}
		
}

else 
{
	ns0#update: {
		ns01#record @(xsi#"type": if(vars.vProductType == 'NonInventorySaleItem')"ns01:NonInventorySaleItem" else "ns01:ServiceSaleItem", "internalId": (vars.vInternalId as Number)): {
			(ns01#taxSchedule @(internalId: "1"): {
				ns02#name: payload.product.taxSchedule
			}) if(payload.product.taxSchedule != null),
			(ns01#displayName: payload.product.displayName) if(payload.product.displayName != null) ,
			(ns01#salesDescription: payload.product.salesDescription) if(payload.product.salesDescription != null) ,
			(ns01#itemId: payload.product.'eid') if(payload.product.'eid' != null),
			ns01#customFieldList: {
				(StringCustomFieldRef__custitem_syncmessage: {
					ns02#value: payload.product.'custitem_syncmessage'
				}) if(payload.product.'custitem_syncmessage' != null) ,
				(SelectCustomFieldRef__custitem_itemsyncstatus: {
                    ns02#value @(internalId: payload.product.'custitem_itemsyncstatus'): {}
                }) if(payload.product.'custitem_itemsyncstatus' != null) ,
				(StringCustomFieldRef__custitem_item_updated_by: {
					ns02#value: payload.product.'itemupdatedby'
				}) if(payload.product.'itemupdatedby' != null) 
			}
			
			}
		}
		
}